@charset "utf-8";

body {
	background-color: #F4F4F4;
	line-height: 1.5;
}

* {
	font-family: 'IBM Plex Sans', sans-serif;
	box-sizing: border-box;
}

h1 {
	font-weight: 600;
	font-size: 54px;
	line-height: 1.2;
}

h3 {
	font-weight: bold;
	font-size: inherit;
	margin: 0;
}

h3 + p {
	margin-top: 0;
}

p + ul, p + ol {
	margin-top: -20px;
	margin-bottom: 2rem;
}

#vnitrek {
	max-width: 1400px;
	margin: 0 auto;
	padding: 0 2em;
	margin-bottom: 4em;
}

.flex {
	display: flex;
	gap: 2rem;
}

.flex main {
	width: 60%;
}

.flex aside {
	width: 40%;
}

label.flex {
	gap: .33em;
}

.flex aside {
	order: 10
}

form main {
	background-color: #fff;
	border-radius: .62rem;
	padding: 1.2rem;
}

main p:first-child {
	margin-top: 0;
}

input[type=text], input[type=email], input[type=tel], textarea {
	width: 100%;
	border: 1.5px solid #ccc;
	border-radius: 6px;
	padding: 1ex 1.5ex;
	font-size: inherit;
}

input[type=submit] {
	background-color: #05145C;
	color: #fff;
	font-weight: 600;
	border: none;
	border-radius: 6px;
	padding: 3ex;
	font-size: inherit;
	cursor: pointer;
}

input[type=checkbox] {
	zoom: 2;
	position: relative;
	top: -2px;
}

input:focus, textarea:focus {
	outline: #05145C solid 3px;
}

form label {
	display: block;
	margin-top: 1.3rem;
	margin-bottom: .2rem;
	cursor: pointer;
	font-weight: 600;
}

label small {
	font-weight: normal;
}

form header {
	display: flex;
	gap: 2rem;
}

form header label {
	position: absolute;
	top: -100rem;
	left: -100rem;
}

form header select {
	padding: 2ex;
    border: none;
    box-shadow: 0 2px 4px #2225;
    border-radius: .6ex;
    font-weight: 600;
}

p {
	margin-top: 20px;
	margin-bottom: 20px;
}

form .flex p {
	margin: 0;
}

input {
	border-radius: 8px;
}

.priceBox {
	display: inline-block;
	padding-top: 1ex;
	border-top: 1.5px solid #ccc;
}

.submitBox {
	float: right;
	margin: 1.5rem 0;
}

.termsBox {
	font-size: small;
}

.termsBox a {
	color: inherit;
}

footer {
	margin-top: 5rem;
}

.toggle {
	display: none;
}

#student-blok {
	height: 0;
	width: 0;
	overflow: hidden;
	display: none;
	transition: width 1s ease-in-out;
}

#student-blok.show {
	height: initial;
	display: block;
	width: 100%;
}

@media screen and (max-width: 666px) {
	body {
		margin: 0;
	}
	h1 {
		font-size: 46px;
	}
	#vnitrek {
		padding: 0;
	}

	h1, .flex aside, form > header, form > p, form main {
		padding-left: 1.4rem;
		padding-right: 1.4rem;
	}

	.flex main {
		border-radius: 0;
	}

	.flex aside {
		order: 0;
	}
	div.flex, header {
		flex-direction: column;
		gap: 0;
	}

	.flex main, .flex aside {
		width: initial;
	}
}