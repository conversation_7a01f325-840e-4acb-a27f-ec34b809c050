<?php
/**
 * Registrace na testování a odeslání potvrzení o registraci na test
 *
 * Soubor
 * ftp://ku.rage.cz/mensaweb/modules/reg_test_iq.php
 *
 * Kódování
 * win-1250
 *
 * Řádky
 * LF (Unix/Linux), DŮLEŽITÉ !!! (kvůli mailu)
 *
 * Poznamka:
 * If messages are not received, try using a LF (\n) only. Some Unix mail transfer agents (most notably » qmail) replace LF by CRLF automatically (which leads to doubling CR if CRLF is used). This should be a last resort, as it does not comply with » RFC 2822.
 *
 * Změnovník
 * 2022-Jan-11, HK: zdurazneni nutnosti kliknout na potvrzovani odkaz
 * 2021-Nov-23, HK: predavani jmena testovaneho do prihlasky placene poukazem
 * 2021-Sep-07, HK: pridani nepovinneho pole trida pro deti
 * 2018-May-15, TK, JD: doplnění zasí<PERSON>án<PERSON> pří<PERSON>hu s invitem.
 * 2018-May-08, TK: integrace kodu VK, prihlaska nastavena jako standalone.
 * 2017-Oct-27, VK: doplněn email zákonného zástupce a nahrazen odesílatel mensou pro ochranu před označením za spam
 * 2016-Jan-31, TK: doplnena moznost zadat kod, preformatovano
 * 2015-May-19, PM; doplnění typu testování (minimální věk testovaného) do výběru. umyslne nepoužita pole z tabulky mc_typy_akci - dlouhé texty
 * 2013-Jan-02, TK: Kontrola uložení souboru - nové řádky MUSÍ být uloženy jako UNIX (LF), jinak dojde k poškození hlavičky a špatnému rozpoznání kódování.
 * 2012-Dec-05, TK: Zavedení změnovníku. Přidání informací o transfer encoding.
 */

use database\model\mensaweb\ModelFactory;
use mailer\MailerFactory;
use vcalendar\VCalendarFactory;

/** @noinspection PhpUndefinedVariableInspection */ $mensaweb = $intranet->getMensaweb();

// funkce pro overeni veku
// je_treba_zz() a konstanta VEK_PRO_ZZ
require LIB_2013_DIR . "/mitch.php";

// TODO pořádný debordel kódu :)
// TODO přesun tříd do ssl_library_new

/**
 * Class PrihlaskaTesty
 * Trida pro zpracovani prihlasky na test.
 * Autor VK
 */
class PrihlaskaTesty
{
    // TODO remove
    protected $db;

    /**
     * @var ModelFactory
     */
    protected $mensaweb;

    protected $odeslat;
    protected $jmeno;
    protected $jmeno_zz; // jmeno zakonneho zastupce ditete

    // TODO debordel mesto vs. mesto2 vs mesto21

    // toto je mesto TESTU !!!
    protected $mesto;

    protected $telefon;
    protected $mobil;
    protected $email;
    protected $email2; // e-mail zakonneho zastupce ditete
    protected $byt;
    protected $adresa;
    // toto je mesto v korespondecni adrese
    // TK: to je tak rozesrane neprehledne !!!
    protected $mesto2;
    protected $psc;
    protected $byt1;
    protected $adresa1;
    // toto je mesto v trvalem bydlisti
    protected $mesto21;
    protected $psc1;
    protected $datumnar;
    protected $typ_testu;
    protected $termin;
    protected $vek;
    protected $trida; // oznaceni tridy pro testovani ditete ve skole
    protected $poznamka;
    // toto je id akce k prihlaseni - id_a
    protected $id_a;
    // ziskani kodu z formulare
    protected $slevovy_kod;
    protected $ditestudent26;
    protected $individual;
    protected $idPrihlasky;
    protected $price;

    /**
     * @param ModelFactory $mensaweb
     */
    public function __construct(ModelFactory $mensaweb)
    {
        $this->mensaweb = $mensaweb;
    }

    function getPaymentLink()
    {
        return "https://intranet.mensa.cz/prihlaska/platba_form.php?amount={$this->getPrice()}&customId={$this->getIdPrihlasky()}&name={$this->getPayeeFirstName()}&surname={$this->getPayeeLastName()}&tname={$this->getAttendeeFirstName()}&tsurname={$this->getAttendeeLastName()}&email={$this->getEmail()}&mobile={$this->getMobil()}&address={$this->getAdresa()}&city={$this->getMesto2()}&zip={$this->getPsc()}";
    }

    function getOdeslat()
    {
        return $this->odeslat;
    }

    function getJmeno()
    {
        return $this->jmeno;
    }

    function getJmenoZZ()
    {
        return $this->jmeno_zz;
    }

    function getPayeeName()
    {
        return $this->getJmenoZZ() ?: $this->getJmeno();
    }

    function getPayeeFirstName()
    {
        return explode(" ", $this->getPayeeName(), 2)[0];
    }

    function getPayeeLastName()
    {
        $arr = explode(" ", $this->getPayeeName(), 2);

        return count($arr) > 1 ? $arr[1] : "";
    }

    function getAttendeeFirstName()
    {
        return explode(" ", $this->getJmeno(), 2)[0];
    }

    function getAttendeeLastName()
    {
        $arr = explode(" ", $this->getJmeno(), 2);

        return count($arr) > 1 ? $arr[1] : "";
    }

    function getMesto()
    {
        return $this->mesto;
    }

    function getTelefon()
    {
        return $this->telefon;
    }

    function getMobil()
    {
        return $this->mobil;
    }

    function getEmail()
    {
        return $this->email;
    }

    function getEmail2()
    {
        return $this->email2;
    }

    function getByt()
    {
        return $this->byt;
    }

    function getAdresa()
    {
        return $this->adresa;
    }

    function getMesto2()
    {
        return $this->mesto2;
    }

    function getPsc()
    {
        return $this->psc;
    }

    function getByt1()
    {
        return $this->byt1;
    }

    function getAdresa1()
    {
        return $this->adresa1;
    }

    function getMesto21()
    {
        return $this->mesto21;
    }

    function getPsc1()
    {
        return $this->psc1;
    }

    function getDatumnar()
    {
        $sanitized_date = preg_split('/[^\d]+/', $this->datumnar);  // datum narozeni (jak bylo zadano)

        // funkce, ktera se snazi byt hodna na uzivatele, tedy chapat datum zadane s libovolnym oddelovacem
        if (count($sanitized_date) == 3) {
          if (isset($sanitized_date[2]) && mb_strlen($sanitized_date[2]) == 2) {
            if ($sanitized_date[2] >= date("y")) {
              $sanitized_date[2] = "19" . $sanitized_date[2];
            } else {
              $sanitized_date[2] = "20" . $sanitized_date[2];
            }
          }
          
          $this->getDatumnar = join('.',$sanitized_date);
        }
      
        return $this->datumnar;
    }

    function getTermin()
    {
        return $this->termin;
    }

    function getVek()
    {
        return $this->vek;
    }

    function getTrida()
    {
        return $this->trida;
    }

    function getPoznamka()
    {
        return $this->poznamka;
    }

    function getId_a()
    {
        return $this->id_a;
    }

    function getDitestudent26()
    {
        return $this->ditestudent26;
    }

    function getIndividual()
    {
        return false;
        //return $this->individual;
    }

    /**
     * Vrati true, pokud jsou treba udaje o ZZ.
     * @return bool
     */
    function getJe_Treba_ZZ()
    {
        // print_r($this->getTermin()); - toto je jen ID prihlasky
        // print_r($this->getDatumnar());
        
        $res = true;

        $termin_testu = $this->mensaweb->getAkceModel()->getEventWithOrg($this->id_a)['den'];
        $datum = DateTime::createFromFormat("j.n.Y H:i", $termin_testu ?? new DateTime());
        
        if (preg_match("/^\d{1,2}\.\d{1,2}\.\d{4}$/", mb_strtolower($this->getDatumnar())) == 1) {
          $res = intranet\testovani\mitch\je_treba_zz(
              $datum,
              DateTime::createFromFormat("d.m.Y", $this->getDatumnar())
          );
        }
        
        echo("<!-- getJe_Treba_ZZ(): {$res} -->\n");
        return $res;
    }

    /**
     * @param string $field
     * @return string
     */
    function replace($field)
    {
        return str_replace("?", "", str_replace("=", "", trim($field)));
    }

    function setOdeslat($odeslat)
    {
        $this->odeslat = $this->replace($odeslat);
    }

    function setJmeno($jmeno)
    {
        $this->jmeno = $this->replace($jmeno);
    }

    function setJmenoZZ($jmeno_zz)
    {
        $this->jmeno_zz = $this->replace($jmeno_zz);
    }

    function setMesto($mesto)
    {
        $this->mesto = $this->replace($mesto);
    }

    function setTelefon($telefon)
    {
        $this->telefon = $this->replace($telefon);
    }

    function setMobil($mobil)
    {
        $this->mobil = $this->replace($mobil);
    }

    function setEmail($email)
    {
        $this->email = $this->replace($email);
    }

    function setEmail2($email2)
    {
        $this->email2 = $this->replace($email2);
    }

    function setByt($byt)
    {
        $this->byt = $this->replace($byt);
    }

    function setAdresa($adresa)
    {
        $this->adresa = $this->replace($adresa);
    }

    function setMesto2($mesto2)
    {
        $this->mesto2 = $this->replace($mesto2);
    }

    function setPsc($psc)
    {
        $this->psc = $this->replace($psc);
    }

    function setByt1($byt1)
    {
        $this->byt1 = $this->replace($byt1);
    }

    function setAdresa1($adresa1)
    {
        $this->adresa1 = $this->replace($adresa1);
    }

    function setMesto21($mesto21)
    {
        $this->mesto21 = $this->replace($mesto21);
    }

    function setPsc1($psc1)
    {
        $this->psc1 = $this->replace($psc1);
    }

    function setDatumnar($datumnar)
    {
        $this->datumnar = $this->replace($datumnar);
    }

    function setTyp_testu($typ_testu)
    {
        $this->typ_testu = $this->replace($typ_testu);
    }

    function setTermin($termin)
    {
        $this->termin = $this->replace($termin);
    }

    function setVek($vek)
    {
        $this->vek = $this->replace($vek);
    }

    function setTrida($trida)
    {
        $this->trida = $this->replace($trida);
    }

    function setPoznamka($poznamka)
    {
        $this->poznamka = trim($poznamka);
    }

    function setId_a($id_a)
    {
        $this->id_a = (int)$this->replace($id_a);
    }

    function setSlevovy_kod($slevovy_kod)
    {
        $this->slevovy_kod = $this->replace($slevovy_kod);
    }

    function setDitestudent26($ditestudent26)
    {
        return $this->ditestudent26 = $this->replace($ditestudent26);
    }

    function getIdPrihlasky()
    {
        return $this->idPrihlasky;
    }

    function getPrice()
    {
        return $this->price;
    }

    function setPrice($price)
    {
        $this->price = str_replace("?", "", str_replace("=", "", $price));
    }

    public function setFromNet()
    {
        // TODO refaktoring, fucking hell

        echo("<!-- setFromNet() -->\n");
        if (isset($_POST["odeslat"])) $this->setOdeslat($_POST["odeslat"]);
        if (isset($_POST["jmeno"])) $this->setJmeno($_POST["jmeno"]);
        if (isset($_POST["jmeno_zz"])) $this->setJmenoZZ($_POST["jmeno_zz"]);
        if (isset($_REQUEST["mesto"])) $this->setMesto($_REQUEST["mesto"]);
        if (isset($_POST["telefon"])) $this->setTelefon($_POST["telefon"]);
        if (isset($_POST["mobil"])) $this->setMobil($_POST["mobil"]);
        if (isset($_POST["email"])) $this->setEmail($_POST["email"]);
        if (isset($_POST["email2"])) {
          $this->setEmail2($_POST["email2"]);
          
          if ($this->getEmail() == "") $this->setEmail($_POST["email2"]); //email zakonneho zastupce
        }
        if (isset($_POST["byt"])) $this->setByt($_POST["byt"]);
        if (isset($_POST["adresa"])) $this->setAdresa($_POST["adresa"]);
        if (isset($_POST["mesto2"])) $this->setMesto2($_POST["mesto2"]);
        if (isset($_POST["psc"])) $this->setPsc($_POST["psc"]);
        if (isset($_POST["byt1"])) $this->setByt1($_POST["byt1"]);
        if (isset($_POST["adresa1"])) $this->setAdresa1($_POST["adresa1"]);
        if (isset($_POST["mesto21"])) $this->setMesto21($_POST["mesto21"]);
        if (isset($_POST["psc1"])) $this->setPsc1($_POST["psc1"]);
        if (isset($_POST["datumnar"])) $this->setDatumnar($_POST["datumnar"]);
        if (isset($_POST["typ_testu"])) $this->setTyp_testu($_POST["typ_testu"]);
        if (isset($_POST["termin"])) $this->setTermin($_POST["termin"]);
        if (isset($_POST["vek"])) $this->setVek($_POST["vek"]);
        if (isset($_POST["trida"])) $this->setTrida($_POST["trida"]);
        if (isset($_POST["poznamka"])) $this->setPoznamka($_POST["poznamka"]);
        if (isset($_REQUEST["id_a"])) $this->setId_a($_REQUEST["id_a"]);
        if (isset($_POST["slevovy_kod"])) $this->setSlevovy_kod($_POST["slevovy_kod"]);
        if (isset($_POST["ditestudent26"])) $this->setDitestudent26($_POST["ditestudent26"]);
        if (isset($_POST["price"])) $this->setPrice($_POST["price"]);
    }

    /**
     * tato funkce ocekava, ze kod je jiz platny !!!!
     * @param int|string $slevovy_kod
     * @param int $id_prihlasky
     * @return bool
     */
    public function zaregistruj_kupon($slevovy_kod, $id_prihlasky)
    {
        echo("<!-- zaregistruj_kupon() -->\n");

        // nebyl dan kod, neni co registrovat - na vstupu je to retezec
        if (mb_strlen($slevovy_kod) < 1) {
            return false;
        }

        try {
            $poukazy_na_test_model = $this->mensaweb->getPoukazyNaTestModel();
            $poukazy_na_test_model->setVoucherAsApplied($slevovy_kod, $id_prihlasky, false);
        }
        catch (PDOException $e) {
            die("Při zápisu slevového kódu došlo k interní chybě.");
        }

        return true;
    }

    public function IsSetIdA()
    {
        echo("<!-- IsSetIdA() -->\n");

        if (isset($this->id_a)) {
            $akce = $this->mensaweb->getAkceModel()->getFutureEvent($this->id_a);

            if ($akce) {
                $this->termin = $this->id_a;
                $this->mesto = $akce['city'];
            }
            else {
                echo "Neexistující přihláška na testování.";
            }
        }
    }

    /**
     * @param int $slevovy_kod
     * @param string $jmeno_a_prijmeni
     * @return bool|string
     */
    function zkontroluj_slevovy_kod($slevovy_kod, $jmeno_a_prijmeni)
    {
        echo "<!-- overuji platnost slevoveho kodu -->\n";

        // 2020-05-20
        // Sjednocení logiky pro uplatnění slevového kódu u platby a u testování podle diskuze.
        // Kromě práce se jménem: U platby je jméno a příjmení samostatně. Zde u IQ testu je jméno a příjmení dohromady (= ponechána původní logika).
        // Zrušena podmínka pro poukazy bez jména (podle Zuzky budou nyní poukazy pouze na jméno).

        try {
            $poukaz = $this->mensaweb->getPoukazyNaTestModel()->getVoucherCodeForTestCheck(
                $slevovy_kod,
                $jmeno_a_prijmeni
            );

            $akce = $this->mensaweb->getAkceModel()->getByIdA($this->id_a);

            if (!$poukaz) {
                return "Slevový kód {$slevovy_kod} nebyl nalezen nebo kombinace kódu {$slevovy_kod}
                    a jména {$jmeno_a_prijmeni} není správná. Pokud kód nemáte, prosíme, nechte pole zcela prázdné.
                    Slevový kód platí pouze pro konkrétní jméno a lze jej použít maximálně jednou.<br />";
            }
        }
        catch (PDOException $e) {
            return "Při kontrole kódu došlo k interní chybě 1, prosíme napiš<NAME_EMAIL>.<br />";
        }

        if (!$akce) {
            die("Nebyl zvolen termín testování. Pokud se pro dané město neozbrazují žádné termíny, jsou již všechny obsazeny.");
        }

        if ($poukaz['prihlasit_do'] > time()) {
            return "Slevový kód {$slevovy_kod} bylo třeba aktivovat do {$poukaz['prihlasit_do']}.<br />";
        }

        if ($akce['date_start'] > $poukaz['prihlasit_do']) {
            return "Slevový kód {$slevovy_kod} je třeba použít na testování konané nejpozději {$poukaz['prihlasit_do']}.<br />";
        }

        // školní testování
        if ($akce['test'] == 56) {
            return "Toto testování je určeno pouze pro žáky/studenty a zaměstnance vybrané školy.
                    Pokud chcete použít slevový kód {$slevovy_kod}, prosím, domluvte se nejprve s testujícím.<br />";
        }

        // neverejne testovani HK skolky
        if ($akce['test'] == 57) {
            return "Slevový kód {$slevovy_kod} nelze uplatnit na neveřejné testování, prosím, zvolte jiný termín.<br />";
        }

        // korektni exit
        return true;
    }

    public function verifyForm()
    {
        echo "<!-- verifyForm() -->\n";

        $this->id_a = $this->termin;
        $strError = "";
        // existují povinné polozky
        if (mb_strlen($this->jmeno) == 0) {
            $strError .= "Musíte zadat jméno a příjmení tak, jak je uvedeno ve vašem dokladu totožnosti (pokud jej máte).<br />";
        }

        // hledej řetězec velkých písmen delší než 3 znaky
        if (preg_match("/[A-Z]{3,50}/", $this->jmeno) === 1) {
            $strError .= "Jméno, prosíme, nezadávejte velkými písmeny.<br />";
        }

        // hledej více než 1 mezeru
        if (preg_match("/\s{2,50}/", $this->jmeno) === 1) {
            $strError .= "Mezi jméno a příjmení vložte pouze jednu mezeru.<br />";
        }

        // hledej alespoň 1 mezeru -- vyzadovano platebni branou
        if (!preg_match("/\s/", $this->jmeno)) {
            $strError .= "Zadejte jméno včetně příjmení.<br />";
        }

        // 2017-10-23, VK: doplnena kontrola věku, který již byl označen jako povinný, ale nebyl verifikován
        if (preg_match("/^\d{1,3}$/", $this->vek) !== 1) {
            $strError .= "Musíte zadat věk (celé číslo).<br />";
        }

        if ($this->vek) {
            $akce = $this->mensaweb->getAkceModel()->getByIdA($this->id_a);
            if (isset($akce['test'])) {
                $badCategory = false;
                if ($this->vek < 5) {
                    $badCategory = true;
                } elseif ($akce['test'] == 49 && ($this->vek < 9)) {
                    $badCategory = true;
                } elseif ($akce['test'] == 50 && ($this->vek < 14)) {
                    $badCategory = true;
                } elseif ($akce['test'] == 59 && ($this->vek > 9)) {
                    $badCategory = true;
                } elseif ($akce['test'] == 60 && (($this->vek > 13) || ($this->vek < 9))) {
                    $badCategory = true;
                } elseif ($akce['test'] == 61 && ($this->vek > 13)) {
                    $badCategory = true;
                }

                if ($badCategory) {
                    $strError .= 'Testování je určeno pro jinou věkovou kategorii.<br />';
                }
            }
        }

        if ($this->getJe_Treba_ZZ()) {
            // 2017-10-23, VK: doplnena  kontrola formatu emailu zákonného zástupce
            if (preg_match("/^[_a-z0-9-+]+(\.[_a-z0-9-+]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,6})$/", mb_strtolower($this->email2)) !== 1) {
                $strError .= "Musíte zadat platný e-mail zákonného zástupce bez mezer.<br />";
            }
            if (mb_strlen($this->jmeno_zz) == 0 ) {
                $strError .= "Musíte zadat jméno a příjmení zákonného zástupce.<br />";
            }
            // hledej více než 1 mezeru
            if (preg_match("/\s{2,50}/", $this->jmeno_zz) === 1) {
                $strError .= "Mezi jméno a příjmení zákonného zástupce vložte pouze jednu mezeru.<br />";
            }
        } else {
          // 2016-01-31, TK: doplnena silnejsi kontrola formatu emailu
          if (preg_match("/^[_a-z0-9-+]+(\.[_a-z0-9-+]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,6})$/", mb_strtolower($this->email)) !== 1) {
              $strError .= "Musíte zadat platný e-mail.<br />";
          }
        }

        // 2016-01-31, TK: doplnena silnejsi kontrola data narozeni
        // 2018-10-09, AN: trosku vic friendly => mezi cislicema muze byt cokoliv
        if (preg_match("/^\d{1,2}[^\d]+\d{1,2}[^\d]+\d{4}$/", mb_strtolower($this->datumnar)) !== 1) {
            $strError .= "Musíte zadat datum narození ve formátu den měsíc rok, přesněji dd. mm. rrrr.<br />";
        }

        if ($this->termin == 0) {
            $strError .= "Musíte vybrat termín.<br />";
        }

        if ((mb_strlen($this->mobil) > 1) && (preg_match("/^(\+\d{1,4} ?)?\d{3} ?\d{3} ?\d{3}$/", mb_strtolower($this->mobil)) !== 1)) {
            $strError .= "Nesprávně zadaný telefon, prosíme, zadejte jej ve formátu 999 999 999 nebo jej nechte prázdný (případnou mezinárodní předvolbu zadejte s plusem).<br />";
        }


        if (mb_strlen($this->mesto2) == 0) {
            $strError .= "Musíte zadat město.<br />";
        }

        if (mb_strlen($this->psc) == 0) {
            $strError .= "Musíte zadat PSČ.<br />";
        }

        if (empty($strError)) {
            if ($this->getJe_Treba_ZZ() && ($this->vek < intranet\testovani\mitch\get_vek_je_treba_zz())) {
                $this->setEmail($this->email2);
            } else {
                $this->setEmail2('');
            }
        }

        // 2016-01-31, TK: pokud je zadaná sleva, je třeba ověřit, zda je platná
        if (mb_strlen($this->slevovy_kod) > 0) {
            // tady je jmeno jeste nezkonvertovane, tj. v utf-8, tak jak prislo z webu
            $vysledek = $this->zkontroluj_slevovy_kod($this->slevovy_kod, $this->jmeno);
            if ($vysledek !== TRUE) {
                $strError .= $vysledek;
            }
        }

        return $strError;
    }

/**
 * // TODO přesunout do samostatné šablony e-mailu
 * @param string $datum
 * @param string $city
 * @param string $place
 * @param string $testujici
 * @param string $jmeno
 * @param string $paymentLink
 * @return string
 */
function getOznameniPrihlaseniBody($datum, $city, $place, $testujici, $jmeno, $paymentLink)
{
    return "
        <p>Vážená/ý paní/pane,
        
        <p>potvrzujeme zájem osoby {$jmeno} o testování inteligence pořádané společností Mensa Česko.</p>
        
        <p>Rezervoval/a jste si testování dne {$datum} v místě {$city}, {$place}.</p>
        
        <p>Pro dokončení Vaší registrace na testování IQ je potřeba uhradit cenu testování.<br />
        Pokud jste tak již neučinil/a, platbu můžete provést platební kartou nebo použít slevový kód
        na adrese <a href='{$paymentLink}' title='Uplatnění slevového kódu'>{$paymentLink}</a></p>
        
        <p>Více o testování se můžete dozvědět na stránce: <a href='https://mensa.cz/testovani-iq/' title='Testování IQ'>mensa.cz/testovani-iq/</a>.</p>
        
        <p>Pokud máte nějaké dotazy, prosím, obraťte se na mě.</p>
        
        <p>Děkuji Vám za Váš zájem,<br />
        s přáním dobrého výsledku,<br />
        {$testujici}</p>
    ";
}

    /**
     *
     * @return array(0=>$vystupni text, 1=>Jméno a email testujícího);
     */
    public function saveDataForm()
    {
        echo("<!-- saveDataForm() -->\n");

        $event = $this->mensaweb->getAkceModel()->getEventWithOrg($this->termin);

        $datum = $event["den"];
        $city = $event["city"];
        $place = $event["place"];
        $email_org = $event["email"];
        $telefon_org = $event["telefon"];
        $jmeno_org = $event["jmeno"] . " " . $event["prijmeni"];

        $vystupText = "<strong>Vaše registrace proběhla úspěšně.</strong><br />";
        $vystupText .= "Přihlásili jste se na:<br />";
        $vystupText .= "<h4>" . $event["nazev"] . "</h4><strong>" . $datum . ", " . $city . "</strong><em> - " . $place . "</em>";
        $vystupText .= "<br />Testující: " . $event["jmeno"] . " " . $event["prijmeni"];
        $testujici = $event["jmeno"] . " " . $event["prijmeni"];

        if (mb_strlen($email_org) > 6) {
            $vystupText .= "<br /><a href=mailto:" . $email_org . ">" . $email_org . "</a>";
            $testujici .= "<br />" . $email_org;
        }
        if (mb_strlen($telefon_org) > 6) {
            $vystupText .= "<br />tel. " . $telefon_org;
            $testujici .= "<br />tel. " . $telefon_org;
        }

        $vystupText .= "<br />Tyto informace Vám budou zaslány na zadaný email.";
        $txt_termin = "{$datum} - {$city}, {$place}";

        try {
            $this->idPrihlasky = $this->insertDataForm($event['id_owner'], $txt_termin);
        }
        catch (PDOException $e) {
            die("Database error.");
        }

        // registracni funkce pozna, ze kod nebyl dan a neni treba nic zapisovat
        $this->zaregistruj_kupon($this->slevovy_kod, $this->idPrihlasky);
        $mailBody = $this->getOznameniPrihlaseniBody($datum, $city, $place, $testujici, $this->jmeno, $this->getPaymentLink());
        $vystup = array($vystupText, $testujici, $mailBody, $jmeno_org, $email_org, $txt_termin);


        echo("<!-- saveDataForm(): hotovo.");
        return $vystup;
    }

    /**
     * @param int $id_org
     * @param string $txt_termin
     * @return int
     */
    protected function insertDataForm($id_org, $txt_termin)
    {
        $jmeno_zz_value_string = $this->getJe_Treba_ZZ() && $this->jmeno_zz ? $this->jmeno_zz : null;
        $trida_value_string = $this->getJe_Treba_ZZ() && $this->trida ? $this->trida : null;

        $data = [
            "jmeno" => $this->jmeno,
            "jmeno_zz" => $jmeno_zz_value_string,
            "vek" => $this->vek,
            "trida" => $trida_value_string,
            "telefon" => $this->mobil,
            "email" => $this->email,
            "typ_testu" => "test",
            "termin" => $txt_termin,
            "poznamka" => $this->poznamka,
            "datum" => date("Y-m-d H:i:s"),
            "id_a" => $this->getId_a(),
            "id_org" => $id_org,
            "adresa" => $this->adresa,
            "mesto" => $this->mesto2,
            "psc" => $this->psc,
            "datumnar" => $this->datumnar,

            // TODO kontrola následujících wtf polí (převzato z původního dotazu)
            "rc" => $this->telefon,
            "cc" => $this->byt,
            "variabilni" => $this->byt1,

            "vol1" => $this->adresa1,
            "vol2" => $this->mesto21,
            "vol3" => $this->psc1,
            "ip" => $_SERVER['REMOTE_ADDR'],
            "email2" => $this->getJe_Treba_ZZ() ? $this->email2 : "",
        ];

        return $this->mensaweb->getWwwForm1Model()->insertAction($data);
    }

    /**
     * @param string $termin
     * @return string
     */
    public function getBodyKontrolni($termin)
    {
        $mail_zz_text = $this->getJe_Treba_ZZ() ? "E-mail zákonného zástupce: {$this->email2}<br />" : "";
        $poznamka_text = $this->poznamka ? "Poznámka: {$this->poznamka}<br />" : "";

        return "
            <p>
                Jméno a příjmení: {$this->jmeno}<br />
                Telefon: {$this->jmeno}<br />
                E-mail: {$this->email}<br />
                {$mail_zz_text}
                Termín: {$termin}<br />
                Věk: {$this->vek}<br />
                {$poznamka_text}
            </p>
        ";
    }
}


////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
//                           zpracovani formulare                             //
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////


$prihlaska = new \PrihlaskaTesty($mensaweb);
$prihlaska->setFromNet();
$prihlaska->IsSetIdA();

$txt_termin = "";
$strError = "neodeslano";
$odeslat = $prihlaska->getOdeslat();

if (!empty($odeslat) && mb_strlen($odeslat) > 0) {
    echo "<!-- odeslana data, zpracovani -->\n";
    $strError = $prihlaska->verifyForm();

    if ($strError == "") {
        list($vystupText, $testujici, $mailBody, $jmenoOrg, $emailOrg, $txt_termin) = $prihlaska->saveDataForm();

        $event = $mensaweb->getAkceModel()->getByIdA($prihlaska->getId_a());

        $ics = VCalendarFactory::getCalendar()->getEventIcal([
            "date_start" => $event['date_start'],
            "date_end" => $event['date_end'],
            "nazev" => "Testování IQ",
            "jmeno_org" => $jmenoOrg,
            "email_org" => $emailOrg,
            "place" => $event["place"],
            "city" => $event["city"],
        ]);

        $mailer = MailerFactory::getMailer();

        $mailer->sendIcalMail(
            $emailOrg,
            $jmenoOrg,
            [
                $prihlaska->getEmail() => $prihlaska->getJmeno(),
            ],
            "Rezervace termínu testování",
            $mailBody,
            [],
            $ics,
            "TestovaniIQ.ics"
        );

        $mailer->sendDefaultMail(
            "<EMAIL>",
            "Mensa Česko",
            [
                $prihlaska->getEmail() => $prihlaska->getJmeno(),
            ],
            "Potvrzení e-mailu - Nutné kliknout",
            getConfirmMailContent($prihlaska)
        );

        $mailer->sendIcalMail(
            "<EMAIL>",
            "Mensa Česko",
            [
                $emailOrg => $emailOrg,
            ],
            $txt_termin,
            $prihlaska->getBodyKontrolni($txt_termin),
            [],
            $ics,
            "TestovaniIQ.ics"
        );

        $payLink = $prihlaska->getPaymentLink();

        echo "<div style='text-align: center; height: 20em; margin: 4em;'><h2><a href='{$payLink}'>Přejít na stránku platby</a></h2></div>";
        echo "<script>window.location = '{$payLink}'</script>";

        return;

    } else {
        $strError = "<br /><span style='color:red;'>Nezadali jste správně všechny povinné položky. <br /><strong>{$strError}</strong></span>";
        echo $strError;
    }
}


////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
//                               Vypis formulare                              //
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
if (mb_strlen($strError) > 0) {
    ?>

    <h1>Přihláška na testování IQ</h1>

    <form action="test_iq.php" name="my_form" method="post">
        <input type="hidden" name="odeslat_m" value="">

        <header>
            <div>
                <label for="mesto">Místo testu:</label>
                <select id="mesto" name="mesto" onchange="document.my_form.odeslat_m.value='mesto'; document.my_form.submit();">
                    <option value="" selected>...zvolte město...</option>

                    <?php foreach ($mensaweb->getAkceModel()->getCities() as $city): ?>
                        <?php $selected_html = $prihlaska->getMesto() == $city ? " selected" : ""; ?>

                        <option value="<?php echo $city; ?>"<?php echo $selected_html; ?>><?php echo $city; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label for="termin">Termín testu:</label>
                <select name="termin" id="termin" onchange='ShowDetails();'>
                    <option value="0" selected>...zvolte termín...</option>

                    <?php if ($city): ?>
                        <?php foreach ($mensaweb->getAkceModel()->getIqTestsByCity($prihlaska->getMesto()) as $iq_test): ?>
                            <?php // TODO učesat ?>
                            <?php if ($iq_test['kapacita'] == 0 || $iq_test['kapacita'] == "" || $iq_test['kapacita'] > $iq_test['poc_prihlasek']): ?>
                                <?php $selected_html = $prihlaska->getTermin() == $iq_test["id_a"] ? " selected" : ""; ?>

                                <option value="<?php echo $iq_test['id_a']; ?>"<?php echo $selected_html; ?>><?php echo "{$iq_test['den']} {$iq_test['typ_nazev']}: {$iq_test['place']}"; ?></option>
                            <?php else: ?>
                                <option disabled="disabled"><?php echo "{$iq_test['den']} {$iq_test['typ_nazev']}: {$iq_test['place']}"; ?> - plně obsazeno</option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <option value="0" selected>...nejprve vyberte město...</option>
                    <?php endif; ?>
                </select>
            </div>
        </header>

        <p>
            <b>Testující:</b><br>
            <span id="testujici"></span>
        </p>

        <div class="flex">
            <aside>
                <div id="perex"></div>
                <div id="popis"></div>
                <input type="hidden" id="date_start">

                <div>
                    <p>
                        Pro přihlášení na&nbsp;prezenční IQ test Mensy prosíme o&nbsp;vyplnění následující přihlášky na&nbsp;testování a&nbsp;uhrazení ceny testování.<br />
                        <b>500 Kč</b> dospělí<br />
                        <b>300 Kč</b> děti a studenti do 26 let
                    </p>
                    <p>
                        Platit můžete platební kartou nebo slevovým kódem z&nbsp;poukazu.
                    </p>
                    <h3>Storno podmínky</h3>
                    <p>
                        V&nbsp;případě nemoci&nbsp;dítěte přihlášeného na&nbsp;školní testování bude zaplacená&nbsp;částka vrácena&nbsp;zpět.
                    </p>
                    <p>
                        V&nbsp;ostatních případech, pokud se&nbsp;omluvíte do&nbsp;24&nbsp;hodin před&nbsp;testováním, pak&nbsp;získáte slevový&nbsp;kód ve&nbsp;výši částky zaplacené za&nbsp;IQ&nbsp;test, který můžete použít při&nbsp;Vašem
                        přihlášení&nbsp;se na&nbsp;další vámi vybraný termín testování&nbsp;IQ. Při&nbsp;pozdější omluvě z&nbsp;testování vám zaplacená
                        cena testování nebude bohužel vrácena.
                    </p>
                </div>
            </aside>

            <main>
                <p>* označuje povinné údaje. Prosíme, jméno a&nbsp;adresu zadávejte s&nbsp;diakritikou.</p>

                <label for="jmeno">Jméno a příjmení testované osoby *</label>
                <!-- <small>Jak je uvedeno v OP nebo pasu, pokud máte.</small> -->
                <input type="text" id="jmeno" name="jmeno" size="30" value="<?php echo $prihlaska->getJmeno(); ?>">
            
                <label for="mobil">Mobil/telefon</label>
                <input type="hidden" name="telefon">
                <input type="text" placeholder="Ve formátu 999 999 999" id="mobil" name="mobil" size="30" value="<?php echo $prihlaska->getMobil(); ?>">

                <label for="datumnar">Datum narození testované osoby *</label>
                <!-- <small style="color: red;">Ve formátu den.měsíc.rok, resp. DD.MM.RRRR (bez mezer)</small> -->
                <input type="text" placeholder="DD.MM.RRRR" id="datumnar" name="datumnar" size="12" maxlength="10" value="<?php echo $prihlaska->getDatumnar(); ?>">

                <label>Věk</label>
                <input type="hidden" name="vek" value="<?php echo $prihlaska->getVek(); ?>">
                <input type="text" id="vek-preview" value="<?php echo $prihlaska->getVek(); ?>" disabled>

                <p id="student-blok">
                    <label for="ditestudent26" class="flex">
                        <span>
                            <input type="checkbox" id="ditestudent26" name="ditestudent26" <?php echo $prihlaska->getDitestudent26() ? "checked" : "" ?>>
                        </span>
                        <span>
                            <strong>Uplatnit slevu pro dítě nebo studenta do 26 let</strong><br />
                            <small>Prosíme studenty VŠ, aby na&nbsp;testování přinesli studentský průkaz nebo potvrzení o&nbsp;studiu.</small>
                        </span>
                    </label>
                    <input type="hidden" name="price" id="price">
                </p>

                <div class="zz-form-fields-inverse">
                        <label for="email">E-mail testované osoby&nbsp;*</label>
                        <!-- <small>Na tento e-mail zašleme výsledek testu osobám starším 15 let, potvrdíte-li zaslaný odkaz, který vám příjde v samostatném e-mailu.</small> -->
                        <input type="email" placeholder="E-mail pro zaslání výsledeku testu. Nutno potvrdit" id="email" name="email" size="30" value="<?php echo $prihlaska->getEmail(); ?>">
                </div>

                <div id="trida-fields" style="display: none">
                        <label for="trida" id="trida-label">Třída *</label>
                        <small>Označení třídy při testování ve škole (např. 3.A apod.)</small>
                        <input type="text" id="trida" name="trida" size="15" value="">
                </div>
                <div class="zz-form-fields">
                        <label for="jmeno_zz">Jméno a příjmení zákonného&nbsp;zástupce&nbsp;*</label>
                        <input type="text" id="jmeno_zz" name="jmeno_zz" size="30" value="<?php echo $prihlaska->getJmenoZZ(); ?>">
                </div>
                <div class="zz-form-fields">
                        <label for="email2">E-mail zákonného zástupce&nbsp;*</label>
                        <!-- <small>Na tento e-mail zašleme výsledek testu osoby mladší 15 let, potvrdíte-li zaslaný odkaz.</small> -->
                        <input type="text" id="email2" name="email2" size="30" value="<?php echo $prihlaska->getEmail2(); ?>">
                </div>

                    <p>
                        <label>Korespondenční adresa</label>
                        <small>Na tuto adresu vám zašleme výsledek testu poštou, pokud nepotvrdíte váš&nbsp;e-mail.</small>
                    </p>
                    
                        <label for="adresa">Ulice + č.p.&nbsp;*</label>
                        <input type="text" id="adresa" name="adresa" size="30" value="<?php echo $prihlaska->getAdresa(); ?>">

                        <label for="mesto2">Město&nbsp;*</label>
                        <input type="text" id="mesto2" name="mesto2" size="30" value="<?php echo $prihlaska->getMesto2(); ?>">

                        <label for="psc">PSČ *</label>
                        <input type="text" id="psc" name="psc" size="6" value="<?php echo $prihlaska->getPsc(); ?>">

                        <label for="byt">Číslo bytu, jméno firmy, koleje, či&nbsp;jiné jméno na&nbsp;schránce:</label>
                        <input type="text" id="byt" name="byt" size="30" value="<?php echo $prihlaska->getByt(); ?>">

                    <p>
                        <label class="flex">
                            <input type="checkbox" id="toggleAddress" onclick="addressToggle()" />
                            Adresa trvalého bydliště se&nbsp;liší od&nbsp;korespondenční adresy&nbsp;*
                        </label>
                    </p>


                    <div class="toggle">
                        <label for="adresa1">Ulice + č.p.:</label>
                        <input type="text" id="adresa1" name="adresa1" size="30" value="<?php echo $prihlaska->getAdresa1(); ?>">

                        <label for="mesto21">Město:</label>
                        <input type="text" id="mesto21" name="mesto21" size="30" value="<?php echo $prihlaska->getMesto21(); ?>">

                        <label for="psc1">PSČ:</label>
                        <input type="text" id="psc1" name="psc1" size="6" value="<?php echo $prihlaska->getPsc1(); ?>"></td>

                        <label for="byt1">Číslo bytu, jméno firmy, koleje, či&nbsp;jiné jméno na&nbsp;schránce:</label>
                        <input type="text" id="byt1" name="byt1" size="30" value="<?php echo $prihlaska->getByt1(); ?>"></td>
                    </div>

                    <label for="poznamka">Poznámka</label>
                    <textarea id="poznamka" name="poznamka" rows="2"><?php echo $prihlaska->getPoznamka(); ?></textarea>

                <p class="submitBox">
                    <input type="submit" value="Odeslat přihlášku" name="odeslat" id="odeslat">
                </p>

                <p class="priceBox">
                    <strong>Cena testování</strong>
                    <span id="priceView">500</span>&nbsp;Kč
                </p>

                <footer>
                    <p>
                        <strong>Po odeslání přihlášky můžete platit platební kartou nebo uplatnit slevový&nbsp;kód.</strong>
                    </p>

                    <p class="termsBox">
                            Přihlášením objednáváte pro testovanou osobu službu testování IQ poskytovanou Mensou Česko.
                            Provedení této služby zahrnující přihlášení, testování, vyhodnocení testu,
                            zaslání výsledku a případné nabídky členství v Mense Česko s sebou nese nutnost zpracování a
                            uchování osobních údajů testované osoby. Zpracovatelem těchto údajů je Mensa Česko,
                            IČ 45248591, Španielova 1111/19, 16300 Praha 6 - Řepy.
                            Podrobné informace o tom, jak údaje chráníme a zpracováváme a jaká jsou Vaše práva naleznete
                            na adrese <a target="_blank" href="http://www.mensa.cz/gdpr/">www.mensa.cz/gdpr</a>.
                    </p>
                </footer>
            </main>
        </div>
    </form>

    <script type="text/javascript">
            // <![CDATA[

            defaultPrice = 500;
            studentDiscount = 200;

            function addressToggle() {
                var toggle = document.getElementById("toggleAddress");
                var div = document.getElementsByClassName("toggle")[0];
                if (!toggle.checked) {
                    div.style.display = "none";
                } else {
                    div.style.display = "block";
                }
            }

            function updatePrice() {
                var ditestudent26 = document.querySelector('input[name="ditestudent26"]');
                var price = defaultPrice;

                ditestudent26.disabled = false;
                if (ditestudent26.checked) {
                    price -= studentDiscount;
                }


                priceView.innerText = price;
                document.getElementById("price").value = price;
            }

            function update_zz() {
                // skryje nebo zobrazi pole s emailem ZZ
                var vek = document.querySelector('input[name="vek"]').value;
                var fields = document.getElementsByClassName("zz-form-fields");
                var fieldsInverse = document.getElementsByClassName("zz-form-fields-inverse");

                toggleShowStudentBlok(vek);

                if (vek.length > 0 && vek < <?php echo intranet\testovani\mitch\get_vek_je_treba_zz(); ?>) {
                    [].forEach.call(fields, function(element) {
                        element.style.display = "";
                    });
                    [].forEach.call(fieldsInverse, function(element) {
                        element.style.display = "none";
                    });
                }
                else {
                    [].forEach.call(fields, function(element) {
                        element.style.display = "none";
                    });
                    [].forEach.call(fieldsInverse, function(element) {
                        element.style.display = "";
                    });
                }
            }

            // dopln nasjeldujici funkce po nacteni dokumentu
            document.addEventListener("DOMContentLoaded", function(){
                // aktualizuj zobrazeni emailu zz
                update_zz();

                updatePrice();

                document.querySelector('input[name="ditestudent26"]').addEventListener('change', function() { updatePrice() });

                // pri zmene veku aktualizuj zobrazeni mailu ZZ
                document.querySelector('input[name="vek"]').addEventListener('change', function() {
                    update_zz();
                });

                // pri zmene data narozeni dopocti vek
                document.querySelector('input[name="datumnar"]').addEventListener('keyup', function() {
                    recalculateVek();
                });

                /* onChange - vola se pri dokonceni zmeny */
                document.querySelector('input[name="datumnar"]').addEventListener('change', function() {
                  var datnarEl = document.querySelector('input[name="datumnar"]');
                  var explod = datnarEl.value.split(/[^0-9]+/, 3);
                  if (explod.length !== 3 || explod[2].trim().length !== 4) {
                    alert("Prosím, zadejte datum narození ve formátu den. měsíc. rok (DD.MM.RRRR).");
                    datnarEl.focus();
                  }
                });

                /* onChange - vola se pri dokonceni zmeny */
                document.querySelector('input[name="jmeno"]').addEventListener('change', function() {
                  var jmeno = document.querySelector('input[name="jmeno"]');
                  if (jmeno.value.split(" ").length < 2) {
                    alert("Prosím, zadejte jméno včetně příjmení.");
                    jmeno.focus();
                  }
                });
            });

            function recalculateVek() {
                var datnar = document.querySelector('input[name="datumnar"]').value;
                var dateTest = document.getElementById("date_start").value;
                var explod = datnar.split(/[^0-9]+/, 3);
                if (explod.length === 3 && explod[2].length === 4) {
                    var dateNar = new Date(parseInt(explod[2].trim()), parseInt(explod[1].trim())-1, parseInt(explod[0].trim()), 0, 0, 0, 0);
                    if (dateTest.length > 0) {
                        var dateNow = new Date(Date.parse(dateTest));
                    } else {
                        var dateNow = new Date();
                    }
                    var diference = dateDiff(dateNow, dateNar);
                    document.querySelector('input[name="vek"]').value = diference.years;
                    document.querySelector('#vek-preview').value = diference.years;

                    update_zz();
                }
            }

            function toggleShowStudentBlok(age) {
                if (age > 4 && age < 19) {
                    if (!ditestudent26.checked) {
                        ditestudent26.click()
                    }
                } else if (age >= 19 && age < 26) {
                    if (ditestudent26.checked) {
                        ditestudent26.click()
                    }
                    document.querySelector("#student-blok").classList.add("show");
                } else {
                    if (ditestudent26.checked) {
                        ditestudent26.click()
                    }
                    document.querySelector("#student-blok").classList.remove("show");
                }
            }

            /* onChange - vola se pri vyberu testování */
            function ShowDetails() {
                var selectBox = document.getElementById("termin");
                var selectedValue = selectBox.options[selectBox.selectedIndex].value;
                if (selectedValue > 0) {
                    var xmlhttp;
                    xmlhttp = new XMLHttpRequest();
                    xmlhttp.onreadystatechange = function () {
                        if (this.readyState == 4 && this.status == 200) {
                            var myArr = JSON.parse(this.responseText);
                            if ((myArr.popis != undefined) && (myArr.perex != undefined)) {
                                document.getElementById("popis").innerHTML = myArr.popis;
                                document.getElementById("perex").innerHTML = myArr.perex;
                                document.getElementById("date_start").value = myArr.date_start;
                                var testujici =  myArr.jmeno + " " + myArr.prijmeni;
                                if (myArr.email) {
                                    testujici = testujici + "<br>" + myArr.email;
                                }
                                if (myArr.telefon) {
                                    testujici = testujici + "<br>" + myArr.telefon;
                                }

                                document.getElementById("testujici").innerHTML = testujici;

                                if (myArr.test == "56") { // 56 = skolni testovani
                                    document.getElementById("trida").required = true;
                                    document.getElementById("trida-fields").style.display = "block";
                                } else {
                                    document.getElementById("trida").required = false;
                                    document.getElementById("trida-fields").style.display = "none";
                                }
                            } else {
                                document.getElementById("popis").innerHTML = "\n";
                                document.getElementById("perex").innerHTML = "\n";
                                document.getElementById("date_start").value = "";
                                document.getElementById("testujici").innerHTML = "";
                            }
                        }
                        recalculateVek();
                    };
                    xmlhttp.open("GET", "reg_akce_json.php?id_a=" + selectedValue, true);
                    xmlhttp.send();
                }
            };
            /* onload - vola se pri nacteni webu */
            window.onload = ShowDetails();


            function dateDiff(dt1, dt2)
            {
                var ret = {days:0, months:0, years:0};

                if (dt1 == dt2) return ret;

                if (dt1 > dt2)
                {
                    var dtmp = dt2;
                    dt2 = dt1;
                    dt1 = dtmp;
                }

                var year1 = dt1.getFullYear();
                var year2 = dt2.getFullYear();

                var month1 = dt1.getMonth();
                var month2 = dt2.getMonth();

                var day1 = dt1.getDate();
                var day2 = dt2.getDate();

                ret['years'] = year2 - year1;
                ret['months'] = month2 - month1;
                ret['days'] = day2 - day1;

                if (ret['days'] < 0)
                {
                    var dtmp1 = new Date(dt1.getFullYear(), dt1.getMonth() + 1, 1, 0, 0, -1);

                    var numDays = dtmp1.getDate();

                    ret['months'] -= 1;
                    ret['days'] += numDays;
                }

                if (ret['months'] < 0)
                {
                    ret['months'] += 12;
                    ret['years'] -= 1;
                }

                return ret;
            }

            // ]]>
    </script>
    <?php
}


/**
 * TODO přesunout do samostatné šablony e-mailu
 * @param PrihlaskaTesty $prihlaska
 * @return string
 */
function getConfirmMailContent(PrihlaskaTesty $prihlaska)
{
    $confirm_link = "https://{$_SERVER['SERVER_NAME']}/prihlaska/confirm_email_prihlaska.php?email={$prihlaska->getEmail()}&id={$prihlaska->getIdPrihlasky()}";
    $confirm_link_html = "<a href='{$confirm_link}' title='Potvrdit e-mailovou adresu - Nutné kliknout'>{$confirm_link}</a>";

    $mailto_link_html = "<a href='mailto:{$prihlaska->getEmail()}'>{$prihlaska->getEmail()}</a>";

    return "
        <span style=\"display:none; font-size:0px; line-height:0px; max-height:0px; max-widt=
h:0px; opacity:0; overflow:hidden; visibility:hidden; mso-hide:all;\">Bez kliknutí nelze výsledky poslat e-mailem!</span>
        <p>Vážená/ý paní/pane,</p>
        
        <p>Potvrďte Váš e-mail {$confirm_link_html}<br />
        pro zaslání výsledku testování IQ.</p>
        
        <p>Výsledek IQ testu Vám bude odeslán do 2 týdnů od data testování.</p>
        
        <p>Děkuji Vám za Váš zájem,<br />
        s přáním dobrého výsledku,<br />
        Mensa Česko</p>
    ";
}
