<?PHP
/**
 * Registrace na testování POINTS a odeslání potvrzení o registraci na test
 *
 * Kódování
 * UTF-8
 *
 * Řádky
 * LF (Unix/Linux), DŮLEŽITÉ !!!
 *
 * Změnovník
 * 2013-Jan-02, TK: Zavedení změnovníku.
 * Kontrola uložení souboru - nové řádky MUSÍ být uloženy jako UNIX (LF),
 * jinak dojde k poškození hlav<PERSON>ky a špatnému rozpoznání kódování.
 */

use mailer\MailerFactory;

date_default_timezone_set('Europe/Prague');

require_once LIB_2013_DIR . "/mcaptcha.php";

/** @noinspection PhpUndefinedVariableInspection */
$mensaweb = $intranet->getMensaweb();

// Error reporting.
// toto je pro snazsi nalezeni kodu ve strance.
if (FALSE) {
    error_reporting(-1);
    ini_set("display_errors", TRUE);
    ini_set("html_errors", TRUE);
}

// ziskej zaslana data
$odeslat = @$_POST["odeslat"];
$jmeno = @$_POST["jmeno"];
$prijmeni = @$_REQUEST["prijmeni"];
$telefon = @$_POST["telefon"];
$email = @$_POST["email"];
$test = @$_POST["test"];
$vek = @$_POST["vek"];
$datumnar = @$_POST["datumnar"];
$vek = @$_POST["vek"];
$poznamka = @$_POST["poznamka"];
$strError = "neodeslano"; // defaultni error - nevypise se, ale zpusobi zobrazeni formulare

// captcha
// prihlaska se pak tise nezpracuje.
$captcha_value = @$_POST["comment"];
$captcha_class = new MCaptcha(0);

// zpracuj prijata data
if (strlen($odeslat) > 0) {
    $strError = "";
    // existují povinné polozky
    if (count($test) == 0) {
        $strError .= "Musíte vybrat typ testu.<br>";
    }
    if (strLen($jmeno) == 0) {
        $strError .= "Musíte zadat jméno.<br>";
    }
    if (strLen($prijmeni) == 0) {
        $strError .= "Musíte zadat příjmení.<br>";
    }
    if (strLen($email) < 6) {
        $strError .= "Musíte zadat email.<br>";
    }
    if (strStr($telefon, "mensa.cz")) {
        $strError .= "nesprávně zadaný telefon";
    }
    if ($strError == "" && $captcha_value == $captcha_class->get_value()) {

        // zpracuj kazdou ze zaslanych prihlasek
        foreach ($test AS $typ_testu) {
            $id_test_points = $mensaweb->getWwwTestPointsModel()->insertRegistration([
                "jmeno" => $jmeno,
                "prijmeni" => $prijmeni,
                "telefon" => $telefon,
                "email" => $email,
                "vek" => $vek,
                "datumnar" => $datumnar,
                "pozn" => $poznamka,
                "vlozeno" => date("Y-m-d H:i:s"),
                "typ_testu" => $typ_testu,
            ]);

            $mail_subj = "";
            $mailbody = "";

            if ($typ_testu == 1) {
                $variabilni = '53' . substr("0000" . $id_test_points, strlen($id_test_points), 4);
                $mail_subj = "Přihláška na test Typologie osobnosti";

                $mailbody = "
                    <p>Dobrý den,</p>
                    
                    <p>přihlásil/a jste se na test typologie osobnosti. Tento test poskytuje společnost POINTS Psychometry s.r.o.
                    Díky přihlášení prostřednictvím Mensy Česko bude cena Vašeho testu snížena na 380,- Kč.</p>
                    
                    <p>Částku 380,- Kč, prosím, uhraďte převodem na účet č. 2801689708/2010. Váš jedinečný variabilní symbol je: {$variabilni}.
                    Nejpozději do 7 dnů od připsání Vaší platby na náš účet zašleme na Vámi uvedenou e-mailovou adresu voucher s přístupovými údaji pro absolvování testu.
                    Na základě tohoto voucheru můžete absolvovat test kdykoliv po dobu následujících 4 týdnů.</p>
                    
                    <p>V případě dotazů nás neváhejte kontaktovat.</p>
                ";

            } elseif ($typ_testu == 2) {
                $variabilni = '54' . substr("0000" . $id_test_points, strlen($id_test_points), 4);
                $mail_subj = "Přihláška na test Komplexní dotazník";

                $mailbody = "
                    <p>Dobrý den,</p>
                    
                    <p>přihlásil/a jste se na psychologickou diagnostiku usnadňující vhodný výběr studijní dráhy a následného profesního zaměření.
                    Tento test poskytuje společnost POINTS Psychometry s.r.o. Díky přihlášení prostřednictvím Mensy Česko bude cena Vašeho testu snížena na 760,- Kč.</p>
                    
                    <p>Částku 760,- Kč, prosím, uhraďte převodem na účet č. 2801689708/2010. Váš jedinečný variabilní symbol je: {$variabilni}.
                    Nejpozději do 7 dnů od připsání Vaší platby na náš účet zašleme na Vámi uvedenou e-mailovou adresu voucher s přístupovými údaji pro absolvování testu.
                    Na základě tohoto voucheru můžete absolvovat test kdykoliv po dobu následujících 4 týdnů.</p>
                    
                    <p>V případě dotazů nás neváhejte kontaktovat.</p>
                ";
            } elseif ($typ_testu == 4) {
                $variabilni = '54' . substr("0000" . $id_test_points, strlen($id_test_points), 4);
                $mail_subj = "Přihláška na test Komplexní dotazník";

                $mailbody = "
                    <p>Dobrý den,</p>
                    
                    <p>přihlásil/a jste se na psychologickou diagnostiku usnadňující vhodný výběr studijní dráhy a následného profesního zaměření.
                    Tento test poskytuje společnost POINTS Psychometry s.r.o. 
                    Díky předchozímu absolvování vstupního testu do Mensy Česko a přihlášení prostřednictvím Mensy Česko bude cena Vašeho testu snížena na 720,- Kč.</p>
                    
                    <p>Nejprve vyhledáme výsledek Vašeho testu IQ v naší databázi. Po jeho nalezení Vám zašleme e-mail s údaji pro platbu.</p>
                ";
            }

            $mailbody .= "
                <p>S pozdravem</p>
                <p>Mensa Česko<br />
                <a href='mailto:<EMAIL>'><EMAIL></a></p>
            ";

            echo $mailbody;
            echo "<p>Tyto informace Vám budou zaslány na zadaný email.</p>";

            $email_org = "<EMAIL>";
            $jmeno_org = "Mensa Česko";

            $mailer = MailerFactory::getMailer();

            $mailer->sendDefaultMail(
                $email_org,
                $jmeno_org,
                [
                    $email => $email,
                    '<EMAIL>' => 'Mensa Česko',
                ],
                $mail_subj,
                $mailbody
            );

            $mailbody_org = "
                <p>
                    Jméno: {$jmeno}<br />
                    Příjmení: {$prijmeni}<br />
                    Telefon: {$telefon}<br />
                    E-mail: <a href='mailto:{$email}'>{$email}</a><br />
                    Věk: {$vek}<br />
                    Poznámka: {$poznamka}
                </p>
            ";

            $mailer->sendDefaultMail(
                $email_org,
                $jmeno_org,
                [
                    $email_org => $email_org,
                ],
                "Přihláška na test POINTS z www.mensa.cz",
                $mailbody_org
            );

            echo "<p><b>Vaše registrace proběhla úspěšně.</b><br><br></p>";
        } //foreach - konec zpracovani prihlasek
    } else {
        // vypis chyb ve formulari
        $strError = "<br><font color=red>Nezadali jste všechny povinné položky. <br>" . $strError . "</font>";
        echo $strError;
    }
}

// pokud neni v promenne chyba nic, znamena to, ze probehlo ulozeni dat
// tj. uz neni treba nic dalsiho delat a nepokracovat ve zobrazeni formulare.
// Pokud je stranka zobrazena nove, je v promenne ulozeno "neodeslano".
if (strlen($strError) < 1) return;

// hlaska kontrola captchy
if ($captcha_value != $captcha_class->get_value() && $strError != "neodeslano"){
    // captcha warning
    echo "<p style='margin-bottom: 60px; color: red;'>Přihlášení selhalo. 
          Pokud nejste spamovací robot, prosím, akci opakujete nebo kontaktujte organizátora.</p>";
}

?>

<h1>Přihláška na doplňkové testy</h1>
<p>Toto testování nabízí Mensa ve spolupráci se společností Institute of Applied Psychology, s.r.o.</p>

<form name="my_form" method="post" action="">
    <input type="hidden" name="odeslat_m" value="">

    <p>Prosím, zvolte typ testu (můžete i více):</p>
    <table border="0" cellpadding="2" cellspacing="3">
        <tr>
            <td>
                <input type="checkbox" name="test[]" value="1">
            </td>
            <td>
                <strong>1. Typologie osobnosti (380,- Kč)</strong>
            </td>
        </tr>
        <tr>
            <td>
                <input type="checkbox" name="test[]" value="2">
            </td>
            <td>
                <strong>2. Komplexní dotazník (760,- Kč)</strong> (bez předchozího mensovního testu IQ)
            </td>
        </tr>
        <tr>
            <td>
                <input type="checkbox" name="test[]" value="4">
            </td>
            <td>
                <strong>3. Komplexní dotazník (720,- Kč)</strong> (po předchozím absolvování mensovního testu IQ)
            </td>
        </tr>
    </table>

    <p>
        Vyplňte údaje potřebné pro přihlášení:<br><br>
        <font color="red">*</font> povinné údaje<br>
        <font color="red">**</font> údaj povinný pouze pro variantu 3. <br>
    </p>

    <table border="0" cellpadding="2" cellspacing="3">
        <tr>
            <td style="width: 15em;">
                <?php echo "{$captcha_class->print_label()}"; ?><font color="red">*</font>
            </td>
            <td>
                <?php echo "{$captcha_class->print_input($captcha_value)}"; ?>
            </td>
        </tr>

        <tr>
            <td>
                <label for="jmeno">Jméno:<font color="red">*</font></label>
            </td>
            <td>
                <input type="text" id="jmeno" name="jmeno" size="30" value="<?PHP echo $jmeno; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="prijmeni">Příjmení:<font color="red">*</font></label>
            </td>
            <td>
                <input type="text" id="prijmeni" name="prijmeni" size="30" value="<?PHP echo $prijmeni; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="telefon">Telefon:</label>
            </td>
            <td>
                <input type="text" id="telefon" name="telefon" size="30" value="<?PHP echo $telefon; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="email">E-mail:<font color="red">*</font></label>
            </td>
            <td>
                <input type="text" id="email" name="email" size="30" value="<?PHP echo $email; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="vek">Věk:<font color="red">*</font></label>
            </td>
            <td>
                <input type="text" id="vek" name="vek" size="30" value="<?PHP echo $vek; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="datumnar">Datum narození:<font color="red">**</font></label>
            </td>
            <td>
                <input type="text" id="datumnar" name="datumnar" size="30" value="<?PHP echo $datumnar; ?>">
            </td>
        </tr>
        <tr>
            <td>
                <label for="poznamka">Poznámka:</label>
            </td>
            <td>
                <textarea id="poznamka" name="poznamka" cols="25" rows="5"><?PHP echo $poznamka; ?></textarea>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <p style="text-align: center; background-color: #66bc29; padding: 1em;">
                    <input type="submit" value="Odeslat přihlášku" name="odeslat">
                </p>
            </td>
        </tr>
    </table>
</form>

<p>&nbsp;</p>
