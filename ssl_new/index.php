<?php

use config\ConfigFactory;
use database\DatabaseFactory;
use intranet\IntranetFactory;
use mailer\MailerFactory;
use request\Request;
use vcalendar\VCalendarFactory;

/** @deprecated use LIB_DIR for new libraries */
define("LIB_2013_DIR", dirname(__FILE__) . "/../ssl_library_2013");
/** @deprecated use LIB_DIR for new libraries */
define("LIB_NEW_DIR", dirname(__FILE__) . "/../ssl_library_new");

define("CONFIG_DIR", dirname(__FILE__) . "/../ssl_config");
define("LIB_DIR", dirname(__FILE__) . "/../ssl_library");
define("PAGE_DIR", dirname(__FILE__) . "/../ssl_pages_new");

require_once LIB_DIR . "/autoload.php";
require_once LIB_DIR . "/vendor/autoload.php";

$autoload_dirs = [
    LIB_DIR,
];

spl_autoload_register(function($class) use ($autoload_dirs) {
    autoload($class, $autoload_dirs);
});

require_once LIB_NEW_DIR . "/register_globals.i";
require_once LIB_NEW_DIR . "/login.i";
require_once LIB_NEW_DIR . "/database.class.l";
require_once LIB_NEW_DIR . "/get_list.l";

$config_data = [];
require_once CONFIG_DIR . "/config.php";

$config = ConfigFactory::getDefaultConfig($config_data);
$intranet = IntranetFactory::getIntranet($config);

$intranet->setMensasec(DatabaseFactory::getMensasecModel($config));
$intranet->setMensaweb(DatabaseFactory::getMensawebModel($config));
$intranet->setMailer(MailerFactory::getMailer());
$intranet->setVcalendar(VCalendarFactory::getCalendar());

if ($intranet->isDevelEnvironment()) {
    ini_set("display_errors", 1);
    ini_set("html_errors", true);

    error_reporting(E_ALL ^ E_DEPRECATED);
}
else {
    ini_set("display_errors", 0);
    ini_set("html_errors", false);

    error_reporting(0);
}

session_start();
ob_start();

date_default_timezone_set($intranet->getConfig()->getValue("default_timezone"));

// databáze
$db = new database;
$db->Open();

// rozkóduj adresu z proměnné men
// pokud není dán cíl
if (!isset($men)) {
    $men = "men0.0.0.0";
}

preg_match("/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts);

$s1 = @$parts[1];
$s2 = @$parts[2];
$s3 = @$parts[3];
$s4 = @$parts[4];

// vyber data ze session (pokud nejaka jsou)
$login = $_SESSION["login"];
$heslo = $_SESSION["heslo"];
$menu = @$_SESSION["menu"];


//ODHLASENI
// toto je adresa odhlašovací stránky
// session se nici az po vyzvednuti hesla, aby bylo mozne zobrazit odhlasovaci stranku :)
if ($s1 == 1 and $s2 == 1 and $s3 == 1 and $s4 == 0) {
    session_destroy();
}

///////////////////////////////////////////////////////////////////////////////

// TODO Následující bloky zapouzdřit do funkcí a dát mimo index.

// nejsou dostupné přihlašovací údaje
if (empty($login) or empty($heslo)) {
    //echo "chybi login";
    // pozor redirect je ještě na dalších místech!!!

    // nejprve over, zda neni rozepsany prispevek, stránka po zobrazení hodí exit.
    if (isset($_POST['message'])) include LIB_NEW_DIR . "/zachran_prispevek.php";

    // zjisti, zda existuje cíl v menu a pokud ano, pošli ho dál
    $target = "";
    if (isset($GLOBALS["QUERY_STRING"])) $target = "?" . $GLOBALS["QUERY_STRING"];

    // redirect to login, zachovej info o tom, kam šel uživatel původně
    Header("Location: login.php$target");
    ob_end_flush();

    // konec, dál se nejede
    return;
}

///////////////////////////////////////////////////////////////////////////////
// zjisti, zda se uživatel může přihlásit (heslo a login jsou platné)
if (!login($login, $heslo, $log_err_string, $db)) {
    // nemůže, vykopnout!

    // nejprve over, zda neni rozepsany prispevek, stránka po zobrazení hodí exit.
    if (isset($_POST['message'])) include LIB_NEW_DIR . "/zachran_prispevek.php";

    // zjisti, zda existuje cíl v menu a pokud ano, pošli ho dál
    $target = "";
    if (isset($GLOBALS["QUERY_STRING"])) $target = "?" . $GLOBALS["QUERY_STRING"];

    // redirect to login, zachovej info o tom, kam šel uživatel původně
    Header("Location: login.php$target");
    ob_end_flush();
    // konec, dál se nejede
    return;
}

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
// pokud jsme zde, uživatel je přihlášený a proměnné o uživateli naplněné

require_once LIB_NEW_DIR . "/user.l";

$a_user = user($login, $heslo, 0, $db);
// paranoic check
// pokud uživatel neexistuje, odejdi
if (!$a_user) {
    // nejprve over, zda neni rozepsany prispevek, stránka po zobrazení hodí exit.
    if (isset($_POST['message'])) include LIB_NEW_DIR . "/zachran_prispevek.php";

    // zjisti, zda existuje cíl v menu a pokud ano, pošli ho dál
    $target = "";
    if (isset($GLOBALS["QUERY_STRING"])) $target = "?" . $GLOBALS["QUERY_STRING"];

    // redirect to login, zachovej info o tom, kam šel uživatel původně
    Header("Location: login.php$target");
    ob_end_flush();
    // konec, dál se nejede, už nic nezpracovávej
    return;
}

//promazani aktualne prihlasenych, pokud se odhlásí (i to se stává)
// stránka 1.1.1.0 je odhlášení
if ($s1 == 1 and $s2 == 1 and $s3 == 1 and $s4 == 0) {
    // TK: maly hack nesmažeme, jen přepíšeme čas do minulosti
    // když se smaže úplně tak to blne statistiku, minimálně do doby, než se to bude logovat lépe
    $intranet->getMensaweb()->getMembersLoginModel()->updateTimestamp($a_user['id_m']);

    header("location: login.php");
    exit;
}

require_once PAGE_DIR . "/inc/head.i";
require_once LIB_NEW_DIR . "/access.l";

?>

<?php if ($intranet->isDevelEnvironment()) { ?>

<body style="padding-top: 0px; background-color: #ddf;">
    <div id="test-intranet" style="font-size: 20px; text-align: center; color: #000; background-color: #f6f; padding: 10px 15px 10px 15px;">Testovací Intranet <?php echo Request::getServerAddress(); ?></div>
    <?php
} else { ?>

    <body>
        <?php
    } ?>
        <!-- Hlavička -->
        <div id="hlavicka">
            <table id="logo">
                <tr>
                    <td>
                        <a href="/"><img class="logo-mensa" src="images-new/logo-mensa.png" width="158" height="78" alt="Logo Mensy"></a>
                    </td>
                </tr>
            </table>
            <table id="odkazy">
                <tr>
                    <td class="horni_odkazy_menu">
                        <a href="http://www.mensa.cz/" target="_blank">Mensa&nbsp;Česko</a>
                        <a href="https://deti.mensa.cz/" target="_blank">Dětská&nbsp;Mensa</a>
                        <a href="http://casopis.mensa.cz/" target="_blank">Časopis&nbsp;Mensa</a>
                        <a href="https://mensagymnazium.cz/" target="_blank">Mensa&nbsp;gymnázium</a>
                        <a href="http://www.logickaolympiada.cz/" target="_blank">Logická&nbsp;olympiáda</a>
                    </td>
                    <td class="odhlaseni_menu">
                        <?php echo str_replace(" ", "&nbsp;", trim($a_user["titul"] . " " . $a_user["jmeno"] . " " . $a_user["prijmeni"] . (!empty($a_user["titul_za_jmenem"]) ? (", " . $a_user["titul_za_jmenem"]) : ""))); ?>&nbsp;<a href="index.php?men=men1.1.1.0"><button class="odhlaseni-button">Odhlásit</button></a>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Tělo -->
        <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border:0px;">
            <tr>
                <!-- levý soupec -->
                <td valign="top" width="180" style="padding:5px 0 5px 5px;" id="leve-menu">

                    <!-- menu -->
                    <?PHP require_once PAGE_DIR . "/inc/left_menu.i"; ?>

                    <!-- pro nevypršení loginu, aby se všude nenápadně obnovoval -->
                    <iframe src="/iframe.php" width="0" height="0" id="iframe"></iframe>
                </td>

                <!-- hlavní sloupec -->
                <td valign="top" style="padding:5px 0 0 0;">
                    <div id="BDY" style="padding: 5px;">

                        <?php
                            require_once LIB_NEW_DIR . "/get_path.l";

                            $a_path = get_path($men, $db);

                            for ($i = 0; $i < count($a_path); $i++) {
                                $s_path = $a_path[$i]["path"];
                                $s_page = $a_path[$i]["class_abbrev"];
                            }

                            // TODO refaktoring
                            if (access("read", $men, $a_user["id_m"], $db)) {
                                //default stranka pro billboard a kdyz neni dokument definovany v db
                                if (empty($s_path)) {
                                    preg_match("/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts);
                                    $s_page = $parts[1];
                                    $adresa_stranky = realpath(PAGE_DIR . "/billboard/{$s_page}.i");
                                } elseif (!empty($s_content)) {
                                    // vkladani s_content stranky
                                    // s_content je GET proměnná, která umožní v rámci kontextu jedné stránky zobrazit jinou
                                    // používá se například při vyhledávání a zobrazení profilu
                                    $adresa_stranky = realpath(PAGE_DIR . $s_path . $s_content);
                                } else {
                                    // a když nebyl dán s_content, tak vlož stránku z db. rovnou

                                    /** @noinspection PhpUndefinedVariableInspection */
                                    // Hodnota $s_page = 'main.i' se bere z tabulky m_acc_class
                                    $adresa_stranky = realpath(PAGE_DIR . $s_path . $s_page);
                                }

                                $intranet->getMensaweb()->getActivityLogModel()->log(
                                    $a_user['id_m'], [$s1, $s2, $s3, $s4], $adresa_stranky
                                );

                                // TODO Dát do configu nebo konstant.
                                // pro určité stránky pošli záznam o zobrazení
                                // 19 centrální databáze
                                // 3 administrace
                                if (in_array($s1, [3, 19])) {
                                    $mailer = new \mailer\Mailer();
                                    $mailer->sendLoggingMail("<EMAIL>", "send_logging_email.php");
                                }

                                ////////////////////////////////////////////////////////////////////////
                                // vlož kód stránky
                                // realpath() returns FALSE on failure, e.g. if the file does not exist.
                                if ($adresa_stranky) {
                                    require_once $adresa_stranky;
                                } else echo "<h1 style='text-align: center'>Uvedená stránka neexistuje nebo ji nesmíte zobrazit</h1>
                                             <p style='text-align: center;'>Pokud si myslíte, že se jedná o chybu, prosím, napište na
                                                <a href='mailto:<EMAIL>?subject=Chyba%20v%20intranetu'><EMAIL></a>.
                                             </p>";

                                // není přístupný obsah
                            } else require_once PAGE_DIR . "/access_denied.i";
                        ?>
                    </div>

                    <!-- end obsah -->
                </td>
            </tr>

            <tr>
                <td colspan="2" align="center" class="copyright" id="copyright">
                    &copy; Mensa Česko, 2002-<?php echo date("Y") ?>.
                </td>
            </tr>
        </table>
    </body>
</html>
<?PHP ob_end_flush(); ?>