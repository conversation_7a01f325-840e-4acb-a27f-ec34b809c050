<!-- INCLUDE ucp_header.html -->

<table class="tablebg" width="100%" cellspacing="1">
<tr>
	<th colspan="2" valign="middle">{L_TITLE}</th>
</tr>
<!-- IF ERROR -->
	<tr>
		<td class="row3" colspan="2" align="center"><span class="gensmall error">{ERROR}</span></td>
	</tr>
<!-- ENDIF -->
<tr> 
	<td class="row1" width="35%"><b class="genmed">{L_CURRENT_IMAGE}: </b><br /><span class="gensmall">{L_AVATAR_EXPLAIN}</span></td>
	<td class="row2" align="center"><br />
		<!-- IF AVATAR -->{AVATAR}<br /><br /><input type="checkbox" class="radio" name="delete" />&nbsp;<span class="gensmall">{L_DELETE_AVATAR}</span>
		<!-- ELSE --><img src="{T_THEME_PATH}/images/no_avatar.gif" alt="" />
		<!-- ENDIF --></td>
</tr>
<!-- IF not S_AVATARS_ENABLED -->
	<tr>
		<td class="row3" colspan="2" align="center">{L_AVATAR_FEATURES_DISABLED}</td>
	</tr>
<!-- ENDIF -->
<!-- IF S_UPLOAD_AVATAR_FILE -->
	<tr> 
		<td class="row1" width="35%"><b class="genmed">{L_UPLOAD_AVATAR_FILE}: </b></td>
		<td class="row2"><input type="hidden" name="MAX_FILE_SIZE" value="{AVATAR_SIZE}" /><input class="post" type="file" name="uploadfile" /></td>
	</tr>
<!-- ENDIF -->
<!-- IF S_UPLOAD_AVATAR_URL -->
	<tr> 
		<td class="row1" width="35%"><b class="genmed">{L_UPLOAD_AVATAR_URL}: </b><br /><span class="gensmall">{L_UPLOAD_AVATAR_URL_EXPLAIN}</span></td>
		<td class="row2"><input class="post" type="text" name="uploadurl" size="40" value="{AVATAR_URL}" /></td>
	</tr>
<!-- ENDIF -->
<!-- IF S_LINK_AVATAR -->
	<tr> 
		<td class="row1" width="35%"><b class="genmed">{L_LINK_REMOTE_AVATAR}: </b><br /><span class="gensmall">{L_LINK_REMOTE_AVATAR_EXPLAIN}</span></td>
		<td class="row2"><input class="post" type="text" name="remotelink" size="40" value="{AVATAR_REMOTE}" /></td>
	</tr>
	<tr> 
		<td class="row1" width="35%"><b class="genmed">{L_LINK_REMOTE_SIZE}: </b><br /><span class="gensmall">{L_LINK_REMOTE_SIZE_EXPLAIN}</span></td>
		<td class="row2"><input class="post" type="text" name="width" size="3" value="{AVATAR_WIDTH}" /> <span class="gen">px X </span> <input class="post" type="text" name="height" size="3" value="{AVATAR_HEIGHT}" /> <span class="gen">px</span></td>
	</tr>
<!-- ENDIF -->
<!-- IF S_DISPLAY_GALLERY -->
	<tr> 
		<td class="row1" width="35%"><b class="genmed">{L_AVATAR_GALLERY}: </b></td>
		<td class="row2"><strong><a href="{U_GALLERY}">{L_DISPLAY_GALLERY}</a></strong></td>
	</tr>
<!-- ENDIF -->

<!-- IF S_IN_AVATAR_GALLERY -->
	<tr> 
		<th colspan="2">{L_AVATAR_GALLERY}</th>
	</tr>
	<tr> 
		<td class="cat" colspan="2" align="center" valign="middle"><span class="genmed">{L_AVATAR_CATEGORY}: </span><select name="category">{S_CAT_OPTIONS}</select>&nbsp; <input class="btnlite" tabindex="0" type="submit" value="{L_GO}" name="display_gallery" /></td>
	</tr>
	<tr> 
		<td class="row1" colspan="2" align="center">
			<table cellspacing="1" cellpadding="4" border="0">
			<!-- BEGIN avatar_row -->
			<tr> 
				<!-- BEGIN avatar_column -->
					<td class="row1" align="center"><img src="{avatar_row.avatar_column.AVATAR_IMAGE}" alt="{avatar_row.avatar_column.AVATAR_NAME}" title="{avatar_row.avatar_column.AVATAR_NAME}" /></td>
				<!-- END avatar_column -->
			</tr>
			<tr>
				<!-- BEGIN avatar_option_column -->
					<td class="row2" align="center"><input type="radio" class="radio" name="avatar_select" value="{avatar_row.avatar_option_column.S_OPTIONS_AVATAR}" /></td>
				<!-- END avatar_option_column -->
			</tr>
			<!-- END avatar_row -->
			</table>
		</td>
	</tr>
<!-- ENDIF -->

<!-- IF S_DISPLAY_GALLERY or S_IN_AVATAR_GALLERY or S_LINK_AVATAR or S_UPLOAD_AVATAR_URL or S_UPLOAD_AVATAR_FILE or AVATAR -->
	<tr>
		<td class="cat" colspan="2" align="center">{S_HIDDEN_FIELDS}<input class="btnmain" type="submit" name="submit" value="{L_SUBMIT}" />&nbsp;&nbsp;<!-- IF S_IN_AVATAR_GALLERY --><input class="btnlite" type="submit" name="cancel" value="{L_CANCEL}" /><!-- ELSE --><input class="btnlite" type="reset" value="{L_RESET}" name="reset" /><!-- ENDIF --></td>
	</tr>
<!-- ENDIF -->
</table>

<!-- INCLUDE ucp_footer.html -->