<!DOCTYPE html>
<html lang="cs-cz">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Anonymizace centralni databáze</title>
</head>

<body>
<h1>Anonymizace centralni databáze</h1>


<?php
/*
Skript PREPISE udaje v CENTRALNI databazi tak, aby byly anonymni.
POZOR, PREPISUJE DATA V AKTUALNI DATABAZI, SPUSTENI NA PROD BY BYLA KATASTROFA.

Change log:
	Skript v roce 2014 pripravil V. Kapic (na zaklade podkladu TK)
	2017-09-26, TK: Aktualizovano na soucasny stav

Zaznam behu na DEV 2022-02-22:
    Zpracovalo  12 432 položek v tabulce c_m_certifikaty.
    Zpracovalo   9 478 položek v tabulce c_m_evidence_prukazu.
    Zpracovalo 104 653 položek v tabulce c_m_nove.
    Zpracovalo  75 774 položek v tabulce c_m_otestovan.
    Zpracovalo  70 901 položek v tabulce c_m_platby.
    Zpracovalo  47 975 položek v tabulce c_m_transakce.
    Zpracovalo  75 108 položek v tabulce c_m_members.
*/



// Kontrola, zda nepoustime skript nekde kde nemame.
// Prvni uroven ochrany - spusteni je povoleno pouze na DEV serveru.
if (!($_SERVER['SERVER_ADDR'] == "**********" || $_SERVER['SERVER_ADDR'] == "**********" || $_SERVER['SERVER_ADDR'] == "**********" || $_SERVER['SERVER_ADDR'] == "**********" || $_SERVER['SERVER_ADDR'] == "**********"))
    die("Lze spustit pouze na DEV serveru **********-88, jinak znicime nase data!");

// druha uroven ochrany, explicitne zakazat na intranetu
if ($_SERVER['SERVER_ADDR'] == "intranet.mensa.cz") die("Lze spustit pouze na DEV serveru, NIKDY, NIKDY ne na PROD!");




/* zapnout error reporting */
error_reporting(-1);
ini_set("display_errors", 1);
ini_set("html_errors", TRUE);


/* autoload */
define("CONFIG_DIR", dirname(__FILE__) . "/../../ssl_config");
define("LIB_DIR", dirname(__FILE__) . "/../../ssl_library");
define("PAGE_DIR", dirname(__FILE__) . "/../../ssl_pages_new");

require_once LIB_DIR . "/autoload.php";
require_once LIB_DIR . "/vendor/autoload.php";

$autoload_dirs = [
    LIB_DIR,
];

spl_autoload_register(function($class) use ($autoload_dirs) {
    autoload($class, $autoload_dirs);
});

/* pripojit se do DB a nastavit spojeni. */
$vstupni_db = mysqli_connect('localhost','root', '***');
mysqli_query($vstupni_db, "SET NAMES 'utf8';");

/* sifrovani pro moznost prace s hodnotou IQ */
require_once dirname(__FILE__)."/../../ssl_library/crypto/CryptoFactory.php";
// je treba mit dve sifrovaci knihovny, protoze desifrujeme dle hesla z produkce a sifrujeme dle hesla na devu.
$aes_prod = \crypto\CryptoFactory::getAES('***'); // MENSA_MEMBERS_SECRET
$aes_dev = \crypto\CryptoFactory::getAES();

/**
 * Randomizuje retezec a zachovava, ze se stejne retezce randomizuji na stejne jine retezce.
 * @param $puvodni_hodnota
 * @return string
 */
function randomize_string($puvodni_hodnota){
    // osetreni zakladnich stavu
    $puvodni_hodnota = trim($puvodni_hodnota);
    if (mb_strlen($puvodni_hodnota) <= 0 || $puvodni_hodnota == NULL) return '';
    if (mb_strlen($puvodni_hodnota) <= 2) return $puvodni_hodnota;

    // nova rychla verze symetricke anonymizace - pomoci md5
    return str_split(md5($puvodni_hodnota), mb_strlen($puvodni_hodnota))[0];
}


/**
 * Projde hodnotu pole a zpracuje ji dle toho, co se jedna za typ.
 * (nechá být nebo andomizuje dle zadání).
 *
 * @param $nazevPole
 * @param $hodnotaPole
 * @return string
 */
$display_counter = 0;
function zpracujPole($nazevPole, $hodnotaPole)
{
    global $aes_prod, $aes_dev, $display_counter, $vstupni_db;

    if ($nazevPole == '') {
        return '';
    }

    // jmena poli odpovidaji tabulkam
    // mensaweb » m_members
    // mensasec » c_m_otestovan
    switch ($nazevPole) {
        case "prijmeni":
        case "jmeno":
        case "jmeno_zz":
        case "prezdivka":
        case "rodne_prijmeni":
        case "titul":
        case "titul_za_jmenem":
            return mysqli_real_escape_string($vstupni_db, randomize_string($hodnotaPole));
        case "tel_d":
        case "tel_p":
        case "fax_p":
        case "fax_d":
        case "mobil":
        case "telefon":
            if(mb_strlen($hodnotaPole) > 3) return rand(100, 999) . ' ' . rand(100, 999) . ' ' . rand(100, 999);
            else return '';
        case "www":
            if(mb_strlen($hodnotaPole)>1) return 'www.seznam.cz';
    		else return '';
        case "adresa":
            return mysqli_real_escape_string($vstupni_db, randomize_string($hodnotaPole));
        case "byt":
        case "byt1":
            return mysqli_real_escape_string($vstupni_db, $hodnotaPole);
        case 'email':
        case 'email2':
            return '<EMAIL>';
        case 'ulice':
        case 'ulice1':
        case 'mesto':
        case 'mesto1':
        case 'obec':
            return mysqli_real_escape_string($vstupni_db, randomize_string($hodnotaPole));
        case 'psc':
        case 'psc1':
        case 'zamestnani_pozn':
        case 'pozn': // u plateb
            return mysqli_real_escape_string($vstupni_db, $hodnotaPole);
        case 'heslo':
            return '73f8d67a1cf829e3';  // heslo mensa
        case 'iq1':
        case 'iq2':
        case 'iq3':
        case 'mitch_iq_1':
        case 'mitch_iq_2':
        case 'mitch_iq_3':
            if ($hodnotaPole == '' || $hodnotaPole == NULL) {
                return '';
            }

            $puvodni_hodnota = $aes_prod->decrypt($hodnotaPole);
            $display_counter += 1;
            if ($display_counter % 1000 === 0) echo "Desifrovana hodnota {$nazevPole}: '{$puvodni_hodnota}'<br>\n";

            if (is_numeric($puvodni_hodnota)) {
                if ($puvodni_hodnota == '') {
                    return '';
                } elseif ($puvodni_hodnota >= '130') {
                    return $aes_dev->encrypt('133');
                } else {
                    return $aes_dev->encrypt('122');
                }
            }
            return $aes_dev->encrypt($puvodni_hodnota);
        case 'per1':
        case 'per2':
        case 'per3':
        case 'mitch_odpovedi_1':
        case 'mitch_odpovedi_2':
        case 'mitch_odpovedi_3':
            if ($hodnotaPole == '' || $hodnotaPole == NULL) {
                return '';
            }
            $puvodni_hodnota = $aes_prod->decrypt($hodnotaPole);
            if ($puvodni_hodnota == '') {
                return '';
            } elseif (is_numeric($puvodni_hodnota)) {
                return $aes_dev->encrypt(rand(10, 99));
            }
            return $aes_dev->encrypt($puvodni_hodnota);
        case 'datumtestu1':
        case 'datumtestu2':
        case 'datumtestu3':
        case 'mitch_datum_1':
        case 'mitch_datum_2':
        case 'mitch_datum_3':
            // data nejsou zasifrovana a nemeni se - v SQL tato pole vubec nejsou.
            if ($hodnotaPole == '' || $hodnotaPole == NULL) {
                return NULL;
            }
            return mysqli_real_escape_string($vstupni_db, $hodnotaPole);
        case 'poznamka':
            return mysqli_real_escape_string($vstupni_db, $hodnotaPole);
	    case 'zamestnani':
            return 'Pracuji';
        case 'cislo_uctu':
            return mysqli_real_escape_string($vstupni_db, randomize_string($hodnotaPole));
        case 'clencislo':
            // pokud je clen cislo '', vrat null
            return $hodnotaPole == '' ? null : $hodnotaPole;
        case 'datum_narozeni':
        case 'rok_narozeni':
            return $hodnotaPole;
	    case 'cislo':
		    return rand(10,10000);

        default:
            // normalni pole - ponechej jak je
            return mysqli_real_escape_string($vstupni_db, $hodnotaPole);
    }
}



// tady začínají přepočty tabulek
echo  "<h3>Start</h3>\n";
flush ();



// c_m_certifikaty zruší poznámku, kde byla jména
// @TODO: dodelat, nespecha



// c_m_evidence_prukazu, kde byla jména a adresy
mysqli_query($vstupni_db, "UPDATE mensasec.c_m_evidence_prukazu SET 
    jmeno_na_karte  = LEFT(MD5(jmeno_na_karte), LENGTH(jmeno_na_karte)),
    zasilaci_adresa = LEFT(MD5(zasilaci_adresa), LENGTH(zasilaci_adresa))
");
echo "Zpracovano ".mysqli_affected_rows($vstupni_db)." položek v tabulce c_m_evidence_prukazu.<br>";
flush();

// c_m_nove, kde bylo heslo
mysqli_query($vstupni_db, "UPDATE mensasec.c_m_nove SET heslo=MD5(heslo) ORDER BY id_n");
echo "Zpracovano ".mysqli_affected_rows($vstupni_db)." položek v tabulce c_m_nove.<br>";
flush();

// c_m_otestovan, kde jsem nechal iq q percentily, ale zrušil vše ostatní
$vysledek_puvodni=mysqli_query($vstupni_db, "SELECT * FROM mensasec.c_m_otestovan");

while($radek=mysqli_fetch_assoc($vysledek_puvodni)) {
    mysqli_query($vstupni_db, "UPDATE mensasec.c_m_otestovan 
            SET jmeno='".zpracujPole('jmeno', $radek['jmeno'])."',
                prijmeni='".zpracujPole('prijmeni', $radek['prijmeni'])."',
                titul_za_jmenem='".zpracujPole('titul_za_jmenem', $radek['titul_za_jmenem'])."',
                jmeno_zz='".zpracujPole('jmeno_zz', $radek['jmeno_zz'])."',
                datum_narozeni='".zpracujPole('datum_narozeni', $radek['datum_narozeni'])."',
                byt='".zpracujPole('byt', $radek['byt'])."',
                ulice='".zpracujPole('ulice', $radek['ulice'])."',
                mesto='".zpracujPole('mesto', $radek['mesto'])."',
                psc='".zpracujPole('psc', $radek['psc'])."',
                byt1='".zpracujPole('byt', $radek['byt1'])."',
                ulice1='".zpracujPole('ulice', $radek['ulice1'])."',
                mesto1='".zpracujPole('mesto', $radek['mesto1'])."',
                psc1='".zpracujPole('psc', $radek['psc1'])."',
                email='".zpracujPole('email', $radek['email'])."',
                email2='".zpracujPole('email2', $radek['email2'])."',
                mobil='".zpracujPole('mobil', $radek['mobil'])."',
                telefon='".zpracujPole('telefon', $radek['telefon'])."'
                WHERE id='".$radek['id']."'");
}
echo "Zpracovano ".mysqli_num_rows($vysledek_puvodni)." položek v tabulce c_m_otestovan.<br>";
mysqli_free_result($vysledek_puvodni);
unset($vysledek_puvodni);
unset($radek);
flush();



// c_m_platby, kde byla poznámka
mysqli_query($vstupni_db, "UPDATE mensasec.c_m_platby SET pozn=LEFT(MD5(pozn), LENGTH(pozn)) ORDER BY id_p");
echo "Zpracovano ".mysqli_affected_rows($vstupni_db)." položek v tabulce c_m_platby.<br>";
flush();



// c_m_transakce
mysqli_query($vstupni_db, "UPDATE mensasec.c_m_transakce SET cislo_uctu_protistrany=LEFT(MD5(cislo_uctu_protistrany), LENGTH(cislo_uctu_protistrany))");
echo "Zpracovano ".mysqli_affected_rows($vstupni_db)." položek v tabulce c_m_transakce.<br>";
flush();



// c_m_members
$vysledek_puvodni=mysqli_query($vstupni_db, "SELECT * FROM mensasec.c_m_members ORDER BY c_id_m;");
while($radek=mysqli_fetch_assoc($vysledek_puvodni)){
    $query = "UPDATE mensasec.c_m_members 
            SET jmeno='".zpracujPole('jmeno', $radek['jmeno'])."',
                prijmeni='".zpracujPole('prijmeni', $radek['prijmeni'])."',
                rodne_prijmeni='".zpracujPole('prijmeni', $radek['rodne_prijmeni'])."',
                jmeno_zz='".zpracujPole('jmeno_zz', $radek['jmeno_zz'])."',
                titul='".zpracujPole('titul', $radek['titul'])."',
                titul_za_jmenem='".zpracujPole('titul_za_jmenem', $radek['titul_za_jmenem'])."',

                iq1='".zpracujPole('iq1', $radek['iq1'])."',
                iq2='".zpracujPole('iq2', $radek['iq2'])."',
                iq3='".zpracujPole('iq3', $radek['iq3'])."',
                mitch_iq_1='".zpracujPole('mitch_iq_1', $radek['mitch_iq_1'])."',
                mitch_iq_2='".zpracujPole('mitch_iq_2', $radek['mitch_iq_2'])."',
                mitch_iq_3='".zpracujPole('mitch_iq_3', $radek['mitch_iq_3'])."',

                per1='".zpracujPole('per1', $radek['per1'])."',
                per2='".zpracujPole('per2', $radek['per2'])."',
                per3='".zpracujPole('per3', $radek['per3'])."',
                mitch_odpovedi_1='".zpracujPole('mitch_odpovedi_1', $radek['mitch_odpovedi_1'])."',
                mitch_odpovedi_2='".zpracujPole('mitch_odpovedi_2', $radek['mitch_odpovedi_2'])."',
                mitch_odpovedi_3='".zpracujPole('mitch_odpovedi_3', $radek['mitch_odpovedi_3'])."',

                byt='".zpracujPole('byt', $radek['byt'])."',
                ulice='".zpracujPole('ulice', $radek['ulice'])."',
                obec='".zpracujPole('obec', $radek['obec'])."',
                psc='".zpracujPole('psc', $radek['psc'])."',
                byt2='".zpracujPole('byt', $radek['byt2'])."',
                ulice2='".zpracujPole('ulice', $radek['ulice2'])."',
                obec2='".zpracujPole('obec2', $radek['obec2'])."',
                psc2='".zpracujPole('psc', $radek['psc2'])."',
                email='".zpracujPole('email', $radek['email'])."',
                email2='".zpracujPole('email2', $radek['email2'])."',
                mobil='".zpracujPole('mobil', $radek['mobil'])."',
                telefon='".zpracujPole('telefon', $radek['telefon'])."',
                telefon_zam='".zpracujPole('telefon', $radek['telefon_zam'])."',
                adr_pozn='".zpracujPole('poznamka', $radek['adr_pozn'])."',
                adr_pozn2='".zpracujPole('poznamka', $radek['adr_pozn2'])."',
                poznamka='".zpracujPole('poznamka', $radek['poznamka'])."'
            WHERE c_id_m=".$radek['c_id_m'];
    mysqli_query($vstupni_db, $query);
    if (mysqli_affected_rows($vstupni_db) === -1) echo "$q<br><br>\n";
    unset($radek);
}
echo"Zpracovano ".mysqli_num_rows($vysledek_puvodni)." položek v tabulce c_m_members.<br>";
unset($vysledek_puvodni);


?>
<h3>Hotovo!</h3>
</body>
</html>
