<?php

namespace mailer;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;

interface MailerInterface
{
    const CHARSET = "UTF-8";

    const NOREPLY_MAIL_ADDRESS = "<EMAIL>";

    /**
     * @return PHPMailer
     */
    public function getPhpMailer(): PHPMailer;

    /**
     * @param string $from_mail
     * @param string $from_name
     * @param array<string, string> $recipients [name => e-mail address]
     * @param string $subject
     * @param string $html_content
     * @param array<string, string> $attachments [file_path => file_name]
     * @return bool
     */
    public function sendDefaultMail(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $attachments = []
    ): bool;

    /**
     * @param string $from_mail
     * @param string $from_name
     * @param array<string, string> $recipients [name => e-mail address]
     * @param string $subject
     * @param string $html_content
     * @param array $string_attachments
     * @param string $ical_content
     * @param string $ical_file_name
     * @return bool
     */
    public function sendIcalMail(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $string_attachments,
        string $ical_content,
        string $ical_file_name
    ): bool;

    /**
     * @param string $from_mail
     * @param string $from_name
     * @param array<string, string> $recipients [name => e-mail address]
     * @param string $subject
     * @param string $html_content
     * @param array<string, string> $attachments [file_path => file_name]
     * @return PHPMailer
     */
    public function getDefaultMailer(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $attachments = []
    ): PHPMailer;

    /**
     * @param string $from_mail
     * @param string $from_name
     * @return PHPMailer
     */
    public function getAutoSendMailer(
        string $from_mail,
        string $from_name
    ): PHPMailer;

    /**
     * @return string
     */
    public function getLastErrorInfo(): string;
}
