<?php

namespace mailer;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception as PHPMailerException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;

abstract class AbstractMailer implements MailerInterface
{
    protected PHPMailer $php_mailer;

    public function __construct()
    {
        $this->php_mailer = new PHPMailer(true); // Enable exceptions
    }

    /**
     * @inheritDoc
     */
    public function getPhpMailer(): PHPMailer
    {
        return $this->php_mailer;
    }

    /**
     * @inheritDoc
     */
    public function getDefaultMailer(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $attachments = []
    ): PHPMailer
    {
        // Configure SMTP settings (implemented by concrete mailers)
        $this->configureSmtp();

        // Set email content and recipients
        $this->php_mailer->setFrom($from_mail, $from_name);
        $this->php_mailer->Subject = $subject;
        $this->php_mailer->Body    = $html_content;
        $this->php_mailer->AltBody = strip_tags($html_content);
        $this->php_mailer->CharSet = self::CHARSET;

        // Add bulk mail headers to prevent auto-replies from out-of-office systems
        $this->addBulkMailHeaders();

        // Add recipients
        foreach ($recipients as $mail => $name) {
            $this->php_mailer->addAddress($mail, $name);
        }

        // Add attachments
        foreach ($attachments as $file_path => $file_name) {
            $this->php_mailer->addAttachment($file_path, $file_name);
        }

        return $this->php_mailer;
    }

    /**
     * @inheritDoc
     */
    public function sendDefaultMail(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $attachments = []
    ): bool
    {
        try {
            $mailer = $this->getDefaultMailer(
                $from_mail,
                $from_name,
                $recipients,
                $subject,
                $html_content,
                $attachments
            );

            $is_sent = $mailer->send();
            $mailer->clearAllRecipients();

            return $is_sent;
        } catch (PHPMailerException $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function sendIcalMail(
        string $from_mail,
        string $from_name,
        array  $recipients,
        string $subject,
        string $html_content,
        array  $string_attachments,
        string $ical_content,
        string $ical_file_name
    ): bool
    {
        try {
            $mailer = $this->getDefaultMailer(
                $from_mail,
                $from_name,
                $recipients,
                $subject,
                $html_content
            );

            // Add string attachments
            foreach ($string_attachments as $string => $file_name) {
                $mailer->addStringAttachment($string, $file_name);
            }

            // Add iCal attachment
            $mailer->addStringAttachment($ical_content, $ical_file_name);

            $is_sent = $mailer->send();
            $mailer->clearAllRecipients();

            return $is_sent;
        } catch (PHPMailerException $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function getAutoSendMailer(string $from_mail, string $from_name): PHPMailer
    {
        $this->configureSmtp();

        $this->php_mailer->isHTML(true);
        $this->php_mailer->setFrom($from_mail, $from_name);
        $this->php_mailer->CharSet = self::CHARSET;

        // Add bulk mail headers to prevent auto-replies from out-of-office systems
        $this->addBulkMailHeaders();

        return $this->php_mailer;
    }

    /**
     * @inheritDoc
     */
    public function getLastErrorInfo(): string
    {
        return $this->php_mailer->ErrorInfo;
    }

    /**
     * Add headers to prevent auto-replies from out-of-office systems
     */
    protected function addBulkMailHeaders(): void
    {
        // Standard bulk mail precedence header
        $this->php_mailer->addCustomHeader('Precedence', 'bulk');

        // Microsoft Exchange/Outlook auto-response suppression
        $this->php_mailer->addCustomHeader('X-Auto-Response-Suppress', 'All');

        // RFC 3834 standard for auto-generated messages
        $this->php_mailer->addCustomHeader('Auto-Submitted', 'auto-generated');

        // Alternative precedence header for older systems
        $this->php_mailer->addCustomHeader('X-Precedence', 'bulk');

        // List-Unsubscribe header helps identify as mailing list
        $this->php_mailer->addCustomHeader('List-Unsubscribe', '<mailto:' . self::NOREPLY_MAIL_ADDRESS . '>');

        // Additional headers for better deliverability and auto-reply prevention
        $this->php_mailer->addCustomHeader('X-Mailer-Type', 'bulk');
        $this->php_mailer->addCustomHeader('X-Bulk', 'yes');
    }

    /**
     * Configure the PHPMailer instance with SMTP settings.
     * This method must be implemented by concrete mailers.
     */
    abstract protected function configureSmtp(): void;
}
