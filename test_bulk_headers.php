<?php
/**
 * Test script to verify that bulk mail headers are being added correctly
 * This script creates a mailer instance and checks if the bulk headers are present
 */

// Include vendor autoload for PHPMailer and other dependencies
require_once __DIR__ . '/ssl_library/vendor/autoload.php';

// Set up autoloading for local classes
spl_autoload_register(function ($class) {
    $autoload_dirs = [
        __DIR__ . '/ssl_library',
        __DIR__ . '/ssl_library_new'
    ];

    $file_name = str_replace("\\", "/", "{$class}.php");

    foreach ($autoload_dirs as $autoload_dir) {
        $file_path = "{$autoload_dir}/{$file_name}";

        if (file_exists($file_path)) {
            require_once $file_path;
            return;
        }
    }
});

use mailer\MailerFactory;

echo "Testing bulk mail headers implementation...\n\n";

try {
    // Create a mailer instance
    $mailer = MailerFactory::getMailer();
    
    // Get an auto-send mailer (this is what autosendmail.l uses)
    $phpMailer = $mailer->getAutoSendMailer('<EMAIL>', 'Test Sender');
    
    // Get the custom headers
    $customHeaders = $phpMailer->getCustomHeaders();
    
    echo "Custom headers added:\n";
    foreach ($customHeaders as $header) {
        echo "  {$header[0]}: {$header[1]}\n";
    }
    
    // Check for specific bulk mail headers
    $expectedHeaders = [
        'Precedence' => 'bulk',
        'X-Auto-Response-Suppress' => 'All',
        'Auto-Submitted' => 'auto-generated',
        'X-Precedence' => 'bulk',
        'List-Unsubscribe' => '<mailto:<EMAIL>>',
        'X-Mailer-Type' => 'bulk',
        'X-Bulk' => 'yes'
    ];
    
    echo "\nChecking for expected bulk mail headers:\n";
    $allHeadersPresent = true;
    
    foreach ($expectedHeaders as $headerName => $expectedValue) {
        $found = false;
        foreach ($customHeaders as $header) {
            if ($header[0] === $headerName && $header[1] === $expectedValue) {
                $found = true;
                break;
            }
        }
        
        if ($found) {
            echo "  ✓ {$headerName}: {$expectedValue}\n";
        } else {
            echo "  ✗ {$headerName}: {$expectedValue} - NOT FOUND\n";
            $allHeadersPresent = false;
        }
    }
    
    echo "\n";
    if ($allHeadersPresent) {
        echo "SUCCESS: All bulk mail headers are present!\n";
        echo "Out-of-office auto-replies should now be prevented.\n";
    } else {
        echo "ERROR: Some bulk mail headers are missing!\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
