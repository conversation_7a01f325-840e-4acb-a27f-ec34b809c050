<?php

require_once("../ssl_library/vendor/autoload.php");
require_once("../ssl_library_new/put_order.l");

use mailer\GoogleMailer;

// Write to DB
$res = put_order($db, $obsah_objednavky, $a_user["id_m"], $login);
if (!$res) {
    echo "Ulozeni informaci o objednavce selhalo";
    return;
}

// Validate user's email
$email = $a_user["email"];
if (empty($email) || ! filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo "Chyba: Email uživatele je neplatný nebo prázdný.";
    return;
}

// Prepare recipients
$recipients = [
    "<EMAIL>" => "Sekretarka", // Secretary's email
    $email => "" // User's email
];

$googleMailer = new GoogleMailer();

$is_sent = $googleMailer->sendDefaultMail(
    "<EMAIL>",                    // From email
    "Intranet Mensy",                       // From name
    $recipients,                            // Recipients
    "Objednavka vizitek",                   // Subject
    "<pre>" . $obsah_objednavky . "</pre>"  // HTML content
);

if (!$is_sent) {
    echo "Odesilani emailu o objednavce selhalo: " . $googleMailer->getLastErrorInfo();
    return;
}
?>

<p>Děkujeme za Váš zájem.</p>
<p>
    Na email, nastavený ve Vašem profilu, Vám přijde info o objednávce.<br>
    Pokud by došlo k nějakým problémům, kontaktujte nás, prosím, na e-mailu <a href="mailto:<EMAIL>"><EMAIL></a>.
</p>