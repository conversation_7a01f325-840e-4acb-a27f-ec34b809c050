<?PHP

namespace intranet\platby\ucet;

require_once 'platby-cenik.class.php';

// <!-- <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->

/**
  Třída na zpracování transakcí na elektronickém výpise z účtu.
  Lze ji zkonstruovat z řádku popisujícího transakci. Pracuje na exportu
 * ve formátu CVS z LBBW/Expobank CZ.


  Autor

  (c) Tom<PERSON><PERSON> 2012, <EMAIL>


  Algoritmus

  Při vzniku třídy se autmaticky provede kontrola syntaxe řádku.
  Při tom se ur<PERSON>, zda platbě rozumíme - zda je validní.
  To je vleká regexp a potom ještě detailní kontrola dalších hodnot.

  Následně se zavolá klasifikace transakce, ve které se řeš<PERSON>,
  zda je platba zpracovatelná, při ní se určí typ platby a sestaví
  seznamy souvisejících záznamů (členská čísla a data).

  Připojení záznamů
  Po proběhnutí kategorizace se automat pokusí propojit
  nalezená potenciální data se záznamy v databázi a vytvořit linky.

  Samotné připsání platby k členovi řeší jiná komponenta.



  Změnovník
  2017-Aug-12, TK: Přechod no nový formát CSV exportu, kontrola kódu
  2014-Mar-02, TK: Upravy pro rok 2014
  2013-Oct-27, TK: Merge zmen od Jaromire Kryse
  2013-Sep-16, TK: Umí zpracovávat i informace o úrocích a poplatcích (kontrola identifikátoru)
  2013-Jun-25, TK: Doplnění sady komentářů
  2012-12-10, TK: Doplněny funkce na propojení s kartou detaily.
  2012-10-12, TK: Čištění a refaktoring, optimalizace výstupu. Nyní lze třídu považovat za hotovou.
  2012-10-09, TK: Soubor založeno odloučením definice třídy ze stránky plateb.

 */

// pro vyvojarsky koutek - jmeno databaze
// pozor, nektere specaility jsou navazene v kodu, pokud je databaze "test"
$DATABASE = "mensasec";
if ($DATABASE == "test"){
    echo "<p>platby-ucet-class.php: Jsme v testu</p>";
}

/**
 * Prijme 1 retezec ve formatu DDMMRRRR a zkonvertuje jej na
 * retezc se zapisem data vhodnym pro vyhledavani v db.
 *
 * Provede logickou kontrolu data.
 *
 * @param string $vstup retezec ve formatu DDMMRRRR
 *
 * @return string overene datum ve formatu YYYY-MM-DD
 *
 */
function convert_date($vstup)
{
    // pracovni pole
    $datum_kousky = Array();
    if (preg_match("/(?P<den>\d{2})(?P<mesic>\d{2})(?P<rok>\d{4})/i", trim($vstup), $datum_kousky) === 1)
    {
        // over smysluplnost hodnot
        // rok musi byt ze znameho oboru
        if ($datum_kousky['rok'] > 1930 AND $datum_kousky['rok'] < (date("Y") + 2)
            AND $datum_kousky['den'] >= 1 AND $datum_kousky['den'] <= 31
            AND $datum_kousky['mesic'] >= 1 AND $datum_kousky['mesic'] <= 12)
        {
            // teprve nyní jsme si jisti, že to je správně
            // je to string a delka je jiz ok a zahrnuje nulu
            return "{$datum_kousky['rok']}-{$datum_kousky['mesic']}-{$datum_kousky['den']}";
        }
    }

    return "";
}

/*

  Popis polí pro export výpisů a obratů ve formátu CSV z aplikace LBBW Direct, Verze 1.0, 21.6.2012

  Banka si vyhrazuje v zájmu zlepšování obsahu informací možnost bez předchozího upozornění
  doplnit další pole v rámci struktury výpisu. V takovém případě budou nová pole zažazena
  na konec věty a nedojde k narušení formátu a pořadí stávajících polí.

  Soubor výpisu používá kódovou stránku UTF-8.
  POZOR: kód momentálně kvůli intranetu počítá se souborem ve formátu win-1250!!!
 *
 *
 *
  Jednotlivé údaje jsou oddělené středníkem (“;”)
  Textové údaje jsou uvedeny v uvozovkách
 */




/**
 * Základní třída, od ní jsou potom odvození potomci vykonávající práci.
 *
 */
class Transakce
{

    // ulozene informace o platbe
    // konstruktor je naplni

    /**
     * Puvodni textovy zdroj platby, je dostupný vždy.
     *
     * @var string
     */
    protected $radek = NULL;

    /**
     * Informace o tom, zda jsou data tridy platna.
     *
     * @var boolean
     */
    protected $valid = FALSE;

    /**
     * Jestli se podařilo automaticky zatřídit (nikoliv, zda je tajná :D).
     *
     * @var boolean
     */
    protected $classified = FALSE;

    /**
     * Textový popis chyby.
     *
     * @var string
     */
    protected $chyba = "";

    /**
     * Záznam o tom, že byla transakce uložena do databáze.
     *
     * @var boolean
     */
    protected $ulozeno = FALSE;




    // plní se při syntaktické analýza
    // údaje o transakci
    protected $typ_transakce = NULL; // 101 korektni transakce, 102 ostatni transakce
    protected $popis_transakce = NULL;
    protected $reference_banky = NULL; // zásadní údaj, to je PK. při ukládání do db.!
    protected $datum_uctovani = NULL; // datum zaúčtování, jako UNIX timestamp
    protected $vypis = NULL; // cislo vypisu
    protected $castka = NULL; // castka ve float
    protected $vs = NULL; // variabilní - forced na integer
    protected $ks = NULL; // kosntantní - forced na int
    protected $ss = NULL; // specifický
    protected $nazev_protistrany = NULL; // textovy nazev odesilatele
    protected $ucet_protistrany = NULL; // cislo uctu odesilatele včetně kódu banky !!!
    protected $detail = "";   // zbýbvající text mimo zprávy pro příjemce
    // použije se při evidenci pořadí záznamů
    protected static $poradi = 0;

    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    // pracovní metody



    /**
     * Uloží paltbu do db. jako novou transakci.
     *
     * Pro zachování pořadí ve výpise musí být volán ve stejném pořadí
     * v jakém byly platby čteny, respektive uloží transakce do db. tom pořadí
     * v jakém je ukládání voláno.
     *
     * Je užívána potomky.
     *
     * @global Array $a_user údaje o přihlášeném uživateli
     * @param databaseObject $db
     * @return boolean
     */
    private function save_to_db($db)
    {
        // prefix pro jmeno databaze
        global $DATABASE;

        // jen pokud máme handler db. a validní paltbu0
        if ($db == null)
            return FALSE;
        if (!$this->valid)
            return FALSE;

        // údaje o přihlášeném uživateli, užijeme pro evidenci
        global $a_user;


        // nejprve ověř, zda paltba není v db.
        // driver cdb. totiž jinak vypíše celý chybný dotaz
        $overeniQ = $db->Query("SELECT count(*) as pocet
            FROM {$DATABASE}.c_m_transakce WHERE reference_banky = '{$this->reference_banky}'");
        $overeni = $db->FetchAssoc($overeniQ);
        if ($overeni['pocet'] !== '0')
        {
            // pozor, všechny proměnné z db. přijdou jako řetězec, striktní porvnání musí být na string s nulou
            echo "<li>Transakce {$this->reference_banky} již je v databázi, nebudu ji ukládat znovu.</li>";
            return FALSE;
        }


        // zformátuj datum
        $datum = date('Y-m-d', $this->datum_uctovani);

        // sestav SQL
        // používáme pouze interní data, není třeba dělat kontrolu
        // i když to není ono
        $sql = "INSERT INTO {$DATABASE}.c_m_transakce VALUE
        (
            '{$this->reference_banky}', --  `reference_banky` char(16) NOT NULL COMMENT 'pole 12 (L) - reference banky',
             {$this->vypis},            --  `cislo_vypisu` smallint(5) unsigned NOT NULL COMMENT 'pole 4 (D) - číslo výpisu',
             " . (static::$poradi) . ", --  `poradi_ve_vypise`
            '" . $db->getEscapedString($this->radek) . "',           --  `radek_transakce` varchar(1024)
            '',                         --  `uzivatelska_poznamka` varchar(255)             '',
            '',                         --  uzivatelska klasifikace
            '',                         --  `souvisejici_zaznamy`
            'nova',                     --  `stav` enum('nova','ceka','zpracovana')
             {$this->castka},           --  `castka` decimal(9,2) NOT NULL COMMENT 'částka v Kč',
             {$this->vs},               --  `variabilni_symbol` bigint(20) unsigned DEFAULT NULL COMMENT 'variabilní symbol',
            '$datum',                   --  `datum_uctovani` date NOT NULL COMMENT 'pole 9 - datum účtování ',
            '{$this->ucet_protistrany}', -- `cislo_uctu_protistrany` varchar(27) NOT NULL,
            NULL,                       --  `datum_zmeny` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            {$a_user['id_m']},           --  `vlozil` int(9) NOT NULL COMMENT 'id_m osoby, která záznam vytvořila',
            NULL                        --  `editoval` int(9) DEFAULT NULL COMMENT 'id_m osoby, která záznam naposledy uložila',
        )";

        $result = $db->Query($sql);
        if ($result == FALSE)
        {
            // něco je špatně
            echo "<li>Selhal zápis do databáze pro transakci: {$this->reference_banky}.</li>";
            return FALSE;
        }
        else
        {
            if ($db->getAffectedRows() !== 1)
            {
                echo "<li>Selhal zápis do databáze pro transakci: {$this->reference_banky}, zapsal jsem divný počet řádek.</li>";
                return FALSE;
            }
            else
            {
                // vše ok
                // Transakce::$poradi++;
                static::$poradi++;
                $this->ulozeno = TRUE;
                return TRUE;
            }
        }
    }



    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////


    /**
     * Konstruktor
     *
     * Konstrukce tridy z textoveho radku informaci o platbe
     *
     * Provede parsovani radky platby
     * a následně ověří získané údaje a
     * uloží je do separátních proměnných.
     *
     * Tato funkce se stará především o syntaktickou
     * validaci informací o platbě (zda jí rozumíme).
     *
     * To zda jí dokážeme zpracovat budeme řešit později.
     *
     * Pokud je dán odkaz na databází, autoamticky uloží palbu jako novou
     * nový záznam a vzteká se, pokud se to nepovede.
     *
     * @param $radek string:  radek z CSV souboru
     * @param null $db: db. objekt
     */
    function __construct($radek, $db = NULL)
    {
        $this->radek = $radek;  // uchovou text řádku pro budoucí generace
        $this->valid = FALSE;   // na začátku je nevalidní
        // nic nedelej, pokud platba neni korektni
        if ($this->radek == null){
            $this->chyba = "radek == null";
            return null;
        }
        if (trim($this->radek) == '') {
            $this->chyba = "radek == ''";
            return null;
        }
        if (mb_strlen($this->radek) < 32) {
            $this->chyba = "mb_strlen(radek) < 32";
            return null;
        }

        // komentáře k regexp
        // využívá možnost rozsekat řetězec do pojmenovaných proměnných
        // [^;] je zkratka pro cokoliv krom středníku, hodí se tam, kde kvalita hodnoty není jasná
        // proved parsovani a zaroven uloz, zda je hodnota validni
        // při zpracování něčeho v uvozovkách hledá uvozovky, nerespektuje \"
        $matches = Array();
        $decode = preg_match("/^\"(?P<id>\d+)\";(?#              				
            )\"(?P<account>\d+)\";(?#                			
            )\"(?P<currency>\w{3})\";(?#                     	
            )\"(?P<value_date>[0-9.]+)\";(?#       
            )\"(?P<transaction_type>[^\"]+)\";(?#    cokoliv
            )\"(?P<counterparty_name>.*)\";(?#    cokoliv - tady muze byt debilni uvozovka !!!, je treba hledat uvozovku strednik
            )\"(?P<cardholder_name>[^\"]*)\";(?#    cokoliv, prazdne
            )\"(?P<counterparty_account_prefix>\d*)\";(?#    cokoliv, prazdne
            )\"(?P<counterparty_account_number>\w{0,2}\d*)\";(?#    cokoliv, je przadne pro poplatky atd.
            )\"(?P<counterparty_bank_code>\d*)\";(?#    cokoliv
            )\"(?P<details>[^\"]*)\";(?#    cokoliv, prazdne
            
            )\"(?P<message_for_beneficiary>[^\"]*)\";(?#   popis prikazce, muze byt prazdny    
            )\"(?P<amount>-?\d+,\d+)\";(?#    
            )\"(?P<type>[^\"]+)\";(?#    cokoliv
            )\"(?P<type_description>[^\"]+)\";(?#    cokoliv
            )\"(?P<status>[^\"]+)\";(?#    cokoliv
            )\"(?P<status_description>[^\"]+)\";(?#    cokoliv
            )\"(?P<card_transaction_type>[^\"]*)\";(?#    cokoliv, prazdne
            )\"(?P<comment_for_yourself>[^\"]*)\";(?#    cokoliv, prazdne
            )\"(?P<transaction_reference>[^\"]+)\";(?#    cokoliv
            
            )\"(?P<transaction_date>[0-9.]+)\";(?#       
            )\"(?P<request_date>[^\"]*)\";(?#    cokoliv
            )\"(?P<variable_symbol>\d*)\";(?#    
            )\"(?P<specific_symbol>\d*)\";(?#    
            )\"(?P<constant_symbol>\d*)\";(?#    za tim pak muze byt spousta dalsich poli, ktera nas nezajimaji
            )/i", $radek, $matches) === 1;

        if (!$decode) {
            $decode = preg_match("/^\"(?P<id>\d+)\";(?#
            )\"(?P<transaction_date>[0-9.]+)\";(?#
            )\"(?P<amount>-?[0-9\,]+)\";(?#
            )\"(?P<currency>\w{3})\";(?#
            )\"(?P<counterparty_account_number>([0-9\-]+)?)\";(?#
            )\"(?P<counterparty_name>.*)\";(?#
            )\"(?P<counterparty_bank_code>\d*)\";(?#
            )\"(?P<counterparty_bank_name>.*)\";(?#
            )\"(?P<constant_symbol>\d*)\";(?#
            )\"(?P<variable_symbol>\d*)\";(?#    
            )\"(?P<specific_symbol>\d*)\";(?#    
            )\"(?P<poznamka>.*)\";(?#
            )\"(?P<message_for_beneficiary>.*)\";(?#
            )\"(?P<transaction_type>.*)\";(?#
            )\"(?P<provedl>.*)\";(?#
            )\"(?P<upresneni>.*)\";(?#
            )\"(?P<poznamka_ucet>.*)\";(?#
            )\"(?P<bic>.*)\";(?#
            )\"(?P<transaction_reference>\d*)\"/i", $radek, $matches) === 1;

        }


        // pokud paarsovani selhalo, netreba pokracovat
        if (!$decode)
        {
            $this->chyba = "řádek se nepodřilo dekódovat; špatný formát řádku/souboru";
            return 0;
        }



        ///////////////////////////////////////////////////
        // napln jednotlive casti o platbe
        // proved kotnrolu a podrobne parsovani informaci
        // kontrola meny
		
		
        if (isset($matches['status']) && $matches['status'] != 'PROCESSED')
        {
            $this->valid = FALSE;
            $this->chyba = "platba jeste neni zpracovana bankou, ignoruji";
            return 0;   // dal neparsujeme
        }
		
		
        if ($matches['currency'] != 'CZK')
        {
            // jina mena, nezname
            $this->valid = FALSE;
            $this->chyba = "nejedná se o platbu v CZK";
            return 0;   // dal neparsujeme
        }



        // ulož číslo výpisu
        // POZOR, v novem exportu je to jine, zcela unikatni ID, ale to principialne nevadi
        $this->vypis = intval($matches['id']);


        // refernece banky - zásadní hodnota pro uložení do db.
        // \"(?P<12reference_banky>[^\"]{0,16})\";(?#     16va
        $this->reference_banky = ($matches['transaction_reference'] ? $matches['transaction_reference'] : '');

        // ve vyjimecnych priapdech muze obsahovat reference banky spatne znaky (mezery),
        // coz pak rozhodi dalsi pouzivani identifikatoru atd, vykilujeme z ni vsechno
        // krom pismen a cislic a podtrzitek
        $this->reference_banky = trim(preg_replace ("/[^A-Za-z0-9_]/", "_",  $this->reference_banky));


        // Uloz datum uctovani.
        // Musi byt zpracovano nyni, mimo poradi, protze je treba pro dalsi ukony.
        // Na vypise je nyni k dispozici sloupec Value date, Transaction date - po testovani bylo zjisteno, ze
        // historicky byla puvodne pouzivana hodnota, ktera se nachazi ve sloupci Transaction date
        // (realne se ale lisi pouze pro bankovni poplatky), jinak je hosnota identicka.
        $datum_kousky = Array();
        if (preg_match("/(?P<den>\d{1,2})\.(?P<mesic>\d{1,2})\.(?P<rok>\d{4})/i", ($matches['transaction_date'] ? $matches['transaction_date'] : ''), $datum_kousky) !== 1)
        {
            // jina mena, nezname
            $this->valid = FALSE;
            $this->chyba = "nepochopitelné datum";
            return 0;   // dal neparsujeme
        }
        else
        {
            //int mktime ([ int $hour = date("H") [, int $minute = date("i") [, int $second = date("s")
            //  [, int $month = date("n") [, int $day = date("j") [, int $year = date("Y") [, int $is_dst = -1 ]]]]]]] )
            $this->datum_uctovani = mktime(0, 0, 0, $datum_kousky['mesic'], $datum_kousky['den'], $datum_kousky['rok']);
        }




        // ve specialnim pripade platbe uroku je reference banky stejna pro platby kazdy mesic
        // musime doplnit cislo vypisu pro interni rozliseni techto plateb (kde refernece musi byt unikatni)
        // POZOR, to co vidime zde jsou jiz zpracovane indetifikatory, cisla nejsou ve vypise (tam jsou misto podtrzitek mezery)
        if ($this->reference_banky == '001AM02CZK_00001' || $this->reference_banky == '001CPPMCZK_00001'
        || $this->reference_banky == '001CPPMCZK_00009'
        || $this->reference_banky == '001P11ECZK_00001' || $this->reference_banky == '001P3IECZK_00001')
        {
            // refernece muze mit presne 16 znaku
            // nahrad poslednich 5 nul, kde je vzdy 00001 cislem vypisu
            // reseni: sluc puvodni retzec oriznuty na delku 10, podtrzitko a cislo vypisu padovane nulami

            // uprava v roce 2014 - problem, cislo vypisu je kazdy rok od nuly, takze rada se opakuje.
            // nicmene cislo vypisu muze nabyvat hodnot od 0 do 365 nebo podobne, tj. je jeste mozne doplnit prefix roku
            // dle masky RRVVV

            // 2017 - problem je ze na novem exportu neni cislo vypisu. Aby se to nemenilo, nahradim to
            // to cislem dne v roce, coz by mel byt ekvivalent cisla vypisu - stejne se to zmeni, protoze
            // vypisy maji cisla jen v pracovni dny ... takze se to neda repordukovat
            // nastesti to nevadi, jen bude treba Zuzku upozornit.

            // dvojmistny rok
            $rok2 = (int) date ('y', $this->datum_uctovani);
            // cislo dne v roce
            $den = (int) date ('z', $this->datum_uctovani);

            // a jeste prasarna nejvetsi
            // kody 001CPPMCZK_00001 a 001CPPMCZK_00009 se mapuji na stejne cislo, co z je spatne
            // pro ten devitkovy musime udelat vyjimku
            if ($this->reference_banky == '001CPPMCZK_00009') $this->reference_banky ='009CPPMCZK_00009';

            // uprav referenci
            $this->reference_banky = sprintf('%10.10s_%02d%03d', $this->reference_banky, $rok2, $den);
        }



        // kontrola typu, neresime interni transakce banky
        // bereme pozue tuzemské transakce, kody se zmenily v roce 2017
        // DOMESTICPAYMENT, FEE, INTEREST, STANDINGINSTRUCTION
        if (isset($matches['type']) && trim($matches['type'] ? $matches['type'] : '') == '')
        {
            $this->chyba = "nebyl zadán kód transakce";
            $this->valid = FALSE;
            return 0;
        }

        // ulož typ
        $this->typ_transakce = (isset($matches['type']) ? $matches['type'] : ''); // uloz kod
        // 2017: puvodne to bylo v podstate 101 nebo 102, nyni je to:
        // DOMESTICPAYMENT (domaci platba)
        // FEE (poplatky),
        // INTEREST (uroky),
        // STANDINGINSTRUCTION (Inkaso)

        $this->popis_transakce = (isset($matches['transaction_type']) ? $matches['transaction_type'] : ''); // uloz popis
        // Hodnoty na nvých výpisech od roku 2017:
        // Příchozí tuzemská platba
        // Tuzemská platba elektronicky
        // Měsíční poplatek za vedení účtu FOP, PO M
        // Srážková daň,
        // Úrok z kreditního zůstatku na účtu,
        // Příchozí tuzemská prioritní platba,
        // Poplatek za 13 ks tuzemských plateb elektronicky od ...
        // Poplatek za 1 ks odchozích tuzemských inkas elektronicky od ...
        // Příchozí vnitrobankovní platba,
        // Příchozí tuzemská platba na základě inkasa


        // setlocale(LC_NUMERIC, 'en_US'); // ujisti s
        // dekoduj castku, smaz teckove oddelovace tisicu, akorat delaji neplechu
        // predelej desetinou carku na tecku (dekodovani je evidentne nezavisle na locale)
        $this->castka = floatval(strtr(strtr($matches['amount'], Array('.' => '')), Array(',' => '.')));

        // dekoduj VS, pokud je
        $this->vs = intval(trim($matches['variable_symbol']));
        if ($this->vs == 0)
        {
            // bereme i platby bez VS, bude roztřízeno při klasifikaci
            $this->chyba = "nenašel jsem VS";
        }



        // 17text_popisu_transakce3 vetsinou obsahuje SS
        $this->ss =  intval(trim($matches['specific_symbol']));



        // textový název účtu, často člověk, užitečný údaj
        $this->nazev_protistrany = trim($matches['counterparty_name']);



        // kontrola cisla uctu
        if (isset($matches['counterparty_account_number']) && $matches['counterparty_account_number'] == "")
        {
            // to nevadi, pokud cislo uctu chybi, stejne nahrajeme, muzou to byt uroky nebo poplatky
            // ktere se musi evidovat v ucetnictvi
            $this->ucet_protistrany = ""; // prázdný řetezec, protže ukládá se jako string, tj. null by se uložil jako řetězec "null"
        } else
        {
            $predcisli = '';
            if (isset($matches['counterparty_account_prefix'])) {
                $predcisli = ($matches['counterparty_account_prefix'] != "")?("{$matches['counterparty_account_prefix']}-"):("");
            }
            // pokud se dostaneme sem, je ucet ok
            $this->ucet_protistrany = "{$predcisli}{$matches['counterparty_account_number']}/{$matches['counterparty_bank_code']}";
        }


        // detail platby
        // 22detail1 vetsinou obsahuje KS, vyjmeme KS nebo to vložíme celé do detailu
        // ks neni nutny
        $this->ks =  intval(trim($matches['constant_symbol']));


        // zbyle informace
        $this->detail .= trim($matches['message_for_beneficiary']);

        //////////////////////////////////
        // dokončena syntaktická analýza
        // jedině teď můžeme potvrdit úspěšné zpracování
        $this->valid = TRUE;

        $this->typ_podrobny = "not classified";

        // pokud jsme dostali handler na db. ulož výsledky snažení
        if ($db != NULL)
            $this->save_to_db($db);

        // Done
        return $this;
    }



    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    // getters
    /**
     * Vrátí originál řádku z výpisu.
     *
     * @return string
     */
    public function get_radek()
    {
        return $this->radek;
    }



    /**
     * Vrátí popis transakce
     *
     * @return string
     */
    public function get_popis()
    {
        return $this->detail;
    }



    /**
     * Vrátí variabilní symbol jako string.
     *
     * @return string
     */
    public function get_vs()
    {
        $vysledek = sprintf("%d", $this->vs);
        return $vysledek;
    }



    /**
     * Vrátí konstatni symbol jako string.
     *
     * @return string
     */
    public function get_ks()
    {
        $vysledek = sprintf("%d", $this->ks);
        return $vysledek;
    }



    /**
     * Je platba validní?
     *
     * @return boolean
     */
    public function is_valid()
    {
        return $this->valid;
    }




    /**
     * Byla platba uložena do databáze?
     *
     * @return boolean
     */
    public function is_saved()
    {
        return $this->ulozeno;
    }



    /**
     * Vrátí uživatelský popis chyby vzniklé při zpracování.
     *
     * @return string
     */
    public function get_error()
    {
        return $this->chyba;
    }



    /**
     * Nastavi chybu, je-li dosud prazdna. Pokud neni prazdna, nedela nic.
     * @param error: text chyby
     */
    public function set_error($error)
    {
        if ($this->chyba == "")
            $this->chyba = $error;
    }

    /**
     * Vrati castku platby jako float nebo null, pokud platba nebyla zpracovana.
     *
     * @return float
     */
    public function get_castka()
    {
        if ($this->is_valid())
            return $this->castka;
        else
            return null;
    }



    /**
     * Vrátí číslo účtu protistrany (odesílatele) jako string včetně lomítka
     * a kódu banky. Vrátí null, pokud platba není rozpoznaná.
     *
     * @return string
     */
    public function get_cislo_uctu()
    {
        if ($this->is_valid())
            return $this->ucet_protistrany;
        else
            return null;
    }



    /**
     * Vrati nazev uctu - slovni (vetsinou jmeno drzitele uctu).
     * Vrátí null, pokud platba není rozpoznaná.
     *
     * @return string
     */
    public function get_nazev_uctu()
    {
        if ($this->is_valid())
            return $this->nazev_protistrany;
        else
            return null;
    }



    /**
     * Vrati datum platby jako unix timestamp.
     * Vrátí null, pokud platba není rozpoznaná.
     *
     * @return int nebo null pokud datum neni
     */
    public function get_datum()
    {
        if ($this->is_valid())
            return $this->datum_uctovani;
        else
            return null;
    }


    /**
     * Returns True if the accounting date is after the cutoff time for late time fee.
     * @return bool
     */
    public function is_late_fee()
    {
        $datum = $this->get_datum();
        if ($datum === null) return null;
        // n - Numeric representation of a month, without leading zeros
        // d - Numeric representation of day in month, with leading zeros
        // => nd will produce 302 for the 2nd of March
        
        return Cenik::is_late_fee($datum);
    }



    /**
     * Vrati cislo vypisu (interni cislo ze systemu banky, vetsinou jsou vypisy
     * cislovane po dnech). Vrátí null, pokud platba není rozpoznaná.
     *
     * @return int
     */
    public function get_cislo_vypisu()
    {
        if ($this->is_valid())
            return $this->vypis;
        else
            return null;
    }





    /**
     * Vrati referneci banky. Toto je unikátní ID v systému banky, které následně
     * slouží jako primární klíč při ukládání transakcí do databáze (je to string
     * písmen a čísel). Stejně tak se používá pro kódování políček formuláře
     * při výpisu stránky (to kalde zásadní nároky na unikátnost a formát,
     * v Expobank CZ je to zatím ok, potenciálně by změna tohoto identifikátoru mohla
     * představovat problém).
     *
     * Vrátí null, pokud platba není rozpoznaná.
     *
     * @return string
     */
    public function get_reference_banky()
    {
        if ($this->is_valid())
            return $this->reference_banky;
        else
            return null;
    }


    /**
     * Vypiš údaje transakci jako fragment HTML (buďto odstavec nebo buňka
     * tabulky).
     *
     * @param string $typ udává, jak má být řetězec formátovaný
      normal  = prostý text
      table   = buňky tabulky
     * @return string HTML kod
     */
    public function print_s($typ = 'normal')
    {

        // validní řádka
        if ($this->valid)
        {
            if ($typ == 'table')
            {

                //$ucet = strtr($this->ucet_protistrany, Array('-' => '- ', '/' => ' /'));
                $ucet = $this->ucet_protistrany;


                //$vyska = (count($this->relevantni_zaznamy) > 0) ? 'rowspan="2"' : '';
                // vyska je nyni vzdy jedna
                $vyska = '';
                return "<td class='typ' {$vyska}>{$this->typ_podrobny}</td>
                        <td class='datum'>" . strtr(date('d. m. Y', $this->datum_uctovani), Array(' ' => '&nbsp;')) . "</td>
                        <td class='vypis'>{$this->vypis}</td>
                        <td class='castka' style='padding-right: 0.5em;'>{$this->castka}&nbsp;Kč</td>
                        <td class='ucet_protistrany'>{$ucet}</td>
                        <td class='nazev_protistrany'>{$this->nazev_protistrany}</td>
                        <td class='vs'>{$this->vs}</td>
                        <td class='detail'>{$this->detail}</td>\n";
            }
            else
                return date('d. m. Y', $this->datum_uctovani) . " &nbsp; {$this->castka} Kč od {$this->ucet_protistrany} VS: {$this->vs}
                    KS: {$this->ks}  {$this->nazev_protistrany} {$this->detail}";
        }

        // nepochopené/neužitečná řádka
        else
        {
            //            if ($this->typ_transakce == 102)
            //            // validní avšak irelevantní transakce
            //                return "Ostatní transakce";
            //            else
            return "{$this->chyba}: {$this->radek}";
        }
    }

}





////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
/**



 */
class Transakce_z_databaze extends Transakce
{

    // bude doplněno při klasifikaci platby
    // pracovní, plní se při klasifikaci platby
    protected $typ = NULL;     // interní typ platby
    protected $typ_podrobny = NULL;     // interní typ platby
    // MOHOU obsahovat duplicity
    protected $relevantni_cisla = Array(); // zde bude uložen seznam potenciálně souvisejících členských čísel
    protected $relevantni_data = Array(); // zde bude uložen seznam potenciálně souvisejících dat narození
    // plní se při párování
    // v principu by neměl obsahovat duplicity
    // záznamy v cdb. ve formátu c_idm => textový_popis
    protected $relevantni_zaznamy = Array(); // pole, ve kterém jsou uloženy výsledné odkazy na relvantní
    protected $stav = NULL; // stav zpracování
    protected $poznamka = NULL; // uživatelská poznámka
    protected $klasifikace = NULL; // uzivatelska kalsifikace

    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    // getters



    /**
     * Vrati typ platby z pohledu Mensy na zaklade analyzy obsahu platby - viz private function classify().
     * Tato hodnota se neuklada, vypocitvava se pri vytvareni objektu platby na zaklade vlastnosti platby.
     *
     * @see classify()
     * @return string
     */
    public function get_type()
    {
        return $this->typ;
    }


    /**
     * Vrati obsah pole uzivatelska klasifikace - to jak to bude zpracvoano pro ucetni system.
     * Hodnota se predvyplnuje, ale finalni slovo ma uzivatel.
     *
     * -- ukazka obsahu
     * SELECT uzivatelska_klasifikace, COUNT( uzivatelska_klasifikace ) AS POCET
     * FROM `c_m_transakce`
     * GROUP BY uzivatelska_klasifikace
     * ORDER BY Pocet DESC;
     *
     * uzivatelska_klasifikace	POCET
        2016	2284
        2015	2112
        2017	2104
        2014	1934
        2013	1381
        2017dm	786
        2016dm	783
        test	769
        2015dm	660
        +1C	    598
                579
        2014dm	551
        NTC	    390
        +Z	    327
        2013dm	317
        zdo	    294
        +2C	    272
        camp	255
        2017+Z	222
        vratka	203
        2016+Z	201
        points	182
     *
     * @return string
     */
    public function get_klasifikace()
    {
        return $this->klasifikace;
    }



    /**
     * Mapuje typ transakce na HTML barvu, vstupem je interní typ transakce.
     *
     * @param string $typ
     * @return string nazev barvy v HTML
     */
    public static function get_color($typ)
    {
        $prevodnik = Array(
            'ostatni_transakce' => 'Gray',
            'nama_vs' => 'DarkRed',
            'clenske_jednotlivec' => 'DarkGreen',
            'clenske_rodina' => 'DarkOliveGreen',
            'vstup_jednotlivec' => 'Teal',
            'iq_test' => 'Ruby',
            'certifikat' => 'Ruby',
            'neidentifikovana' => 'inherit');

        return($prevodnik[$typ]);
    }

    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    // pracovní rutiny


    /**
     * Vezme string obsaující libovlný počet dat ve formátu DDMMRRRR
     * a uloží data ve formátu vhodném pro hledání v databázi.
     * Zároveň ověří, zda to logicky může být datum.
     *
     * Automaticky uloží data do seznamu potenciálních dat
     * string ve formatu RRRR-MM-DD
     *
     * @param $vstup string na kterem ma byt dekodovani provedeno.
     * @return bool: TRUE nebo FALSE podle toho, zda se něco našlo.
     */
    protected function decode_dates($vstup)
    {
        $nasel = FALSE;
        $vysledky = Array();

        // zjisti, zda jsou v retezci nejaka data
        // oddelena od ostatniho textu
        if (preg_match_all("/\b\d{7,8}\b/i", $vstup, $vysledky) > 0)
        {
            // pro kazdy nalezene
            foreach ($vysledky[0] as $v)
            {
                // zkonvertuj datum na osmimistny format
                $datum = sprintf("%08d", trim($v));
                $vysledek = convert_date($datum);

                if ($vysledek != "")
                {
                    $this->relevantni_data[] = $vysledek;
                    $nasel = TRUE;
                }
            }
        }

        return $nasel;
    }


    /**
     * Projde vstupní string a ověří, zda některé z čísel nemůže být členské číslo
     * Pokud se najdou čísla podezřelá, tak je uloží do pole.
     *
     * Členské číslo může mít standardně 4 až 5 číslic.
     * Standardně jsou v db. uloženy s úvodními nulami pro všechna čísla do 9999.
     *
     * Čísla jsou uložena jako řetězce.
     *
     * @param $vstup string na kterem se ma provadet hledani
     * @return bool: TRUE nebo FALSE podle toho, zda se něco našlo. Vysledek je zapsan do $this->relevantni_cisla
     */
    protected function decode_clc($vstup)
    {
        $nasel = FALSE;
        $vysledky = Array();
        if (preg_match_all("/\b\d{4,5}\b/i", $vstup, $vysledky) > 0)
        {
            // ulož vše krom čísel typu tento a příští rok, neboť ta se tam také objevují běžně
            // a většinou to nejsou členská čísla ale skutečně roky
            foreach ($vysledky[0] as $v)
            {
                if ($v != date("Y") AND ($v != (date("Y") + 1)) AND ($v != (date("Y") - 1)) AND ($v != 0))
                {
                    $this->relevantni_cisla[] = $v;
                    $nasel = TRUE;
                }
            }
        }
        return $nasel;
    }



    /**
     * Cílem je procházet jednotlivé jednoznačně definované typy
     * plateb z pohledu Mensy a označit je pro další práci.
     *
     *
     * Členské číslo může mít 1 až 5 znaků (jako číslice, po odtržení nul)
     * Datum má 7 až 8 znaků (podle toho zda je odtržena první nula).
     *
      Chybu zapisujeme opět do chyby, protože počítáme,
      že ze syntaktické kontroly dorazí jen validní
      platby s chybou prázdnou.

      Musí se volat po syntaktické analýze.

     */
    private function classify()
    {
        // lze zavolat jen na validní platbu
        // tady teda chybu nepřepisujeme
        if ($this->valid != TRUE)
        {
            $this->chyba = 'Analyozvat lze jen syntakticky validované platby.';
            return FALSE;
        }


        // do začátku žádnou neznáme
        $this->typ = "neidentifikovana";

        // nastandardní transakce většinou nemají VS, tak nemá cenu se s nimi dále zabývat pri auto zpracovani,
        // uzivatel je zpracuje manualne. Hodnoty na novem vypise (2017):
        // DOMESTICPAYMENT (domaci platba)
        // FEE (poplatky),
        // INTEREST (uroky),
        // STANDINGINSTRUCTION (Inkaso)
        if (!in_array($this->typ_transakce, array("DOMESTICPAYMENT", "STANDINGINSTRUCTION")) && !empty($this->typ_transakce))
        {
            if ($this->typ_transakce == "FEE")
            {
                $this->typ = "ostatni_transakce";
                $this->typ_podrobny = "bankpopl";
                //$this->poznamka = $this->popis_transakce;
                $this->detail = $this->popis_transakce;
                if ($this->klasifikace == '') $this->klasifikace = "bankpopl"; // kod pro uctovani
                return TRUE;   // dal neparsujeme
            }

            if ($this->typ_transakce == "INTEREST")
            {
                $this->typ = "ostatni_transakce";
                $this->typ_podrobny = "úrok";
                // $this->poznamka = $this->popis_transakce;
                $this->detail = $this->popis_transakce;
                if ($this->klasifikace == '') $this->klasifikace = ($this->castka >= 0)?"urok":"dan"; // kod pro uctovani
                return TRUE;   // dal neparsujeme
            }

            // jina mena, nezname
            $this->typ = "ostatni_transakce";
            $this->typ_podrobny = "neznámý typ transakce: {$this->popis_transakce}";
            // if ($matches['13kod_transakce'] == '102')   $this->chyba = "ostatní transakce (nestandardní)";
            // elseif ($matches['13kod_transakce'] == '201')   $this->chyba = "zahraniční transakce";
            //$this->chyba ='Transakci nelze přiřadit.';
            return FALSE;
        }



        // konotrola typu dle popisu
        // zajímají nás jen příchozí tuzemské platby
        //        if ($this->popis_transakce != 'Příchozí tuzemská platba'
        //            and $this->popis_transakce != 'Tuzemská platba elektronicky'
        //            and $this->popis_transakce != 'Příchozí interní platba'
        //            and $this->popis_transakce != 'Příchozí tuzemská platba na základě')
        if (!in_array($this->popis_transakce, [
            "Příchozí tuzemská platba",
            "Tuzemská platba elektronicky",
            "Příchozí tuzemská prioritní platba",
            "Příchozí vnitrobankovní platba",
            "Příchozí tuzemská platba na základě inkasa",
            "Bezhotovostní příjem",
            "Okamžitá příchozí platba",
            "Platba převodem uvnitř banky",
            "Karetní transakce"]) && !empty($this->popis_transakce))
        {
            $this->typ = "ostatni_transakce";
            $this->typ_podrobny = "nestandardní transakce: {$this->popis_transakce}";
            return FALSE;   // dal neparsujeme
        }


        // platby kartou
        if ($this->popis_transakce == 'Karetní transakce') {
            if (mb_strpos($this->detail, 'CESKA POSTA') !== false) {
                $this->klasifikace = 'posta';
            } elseif (mb_strpos($this->detail, 'Výběr z bankomatu') !== false) {
                $this->klasifikace = 'bankomat';
            }
            $this->typ_podrobny = 'Platba kartou';
            return true;
        }

        // interní převody
        // buď dle částky a popisu nebo dle účtu protistrany
        // pozor, na novem vypise jsou nuly osekane: ($this->ucet_protistrany == '000000-00********/5500')
        // ($this->castka < 0 AND trim($this->detail) == "Z MENSY DO MENSY")
        if (   ($this->ucet_protistrany == '********/5500')
            OR ($this->ucet_protistrany == '**********/2010'))
        {
            $this->typ = "ostatni_transakce";
            $this->typ_podrobny = "interní převod";
            if ($this->klasifikace == '') $this->klasifikace = "zdo"; // kod pro uctovani internich prevodu
            return TRUE;   // dal neparsujeme
        }


        // platba, která nemá VS
        // s tou nic neuděláme (ale musí být označena pro manuální kontrolu)
        if ($this->vs == NULL)
        {
            // jina mena, nezname
            $this->typ = "nama_vs";
            $this->typ_podrobny = "platba nemá VS";

            // zkusme analyozvat alespoň poznámku
            $this->decode_dates($this->detail);  // na data
            $this->decode_clc($this->detail);    // na členská čísla

            return FALSE;
        }



        // nyní máme VS amůžeme přikročit k hlavní analýze
        // ostatni_transakce, nama_vs, clenske_jednotlivec, vstup_jednotlivec, clenske_rodina, neidentifikovana
        $vs_string = sprintf("%d", $this->vs); // potřbujeme mít VS ve stringu pro kontrolu délky


        // nejprve opet specialni pripad
        // vratky a podobné, ty také nekategorizujeme, ale pokusime se priradit clenske cislo, pokud je
        if ($this->castka < 0)
        {
            $this->typ = "ostatni_transakce";
            $this->typ_podrobny = "odchozí platba";

            // i pro tyto platby připojme odkaz na relevnantí záznam
            $this->decode_dates($this->detail);  // na data
            $this->decode_clc($vs_string);       // na vs
            $this->decode_clc($this->detail);

            return FALSE;   // dal neparsujeme
        }



        // platba členského za jednu osobu
        // kontrola formátu VS a částky (po zavedeni pokuty mohou byt ruzne castky
        // testuj castku na to zda je pred nebo po datu pro prirazku za pozdni paltbu
        if ((
                    (!$this->is_late_fee() AND ($this->castka == Cenik::ROCNI_CLENSTVI || $this->castka == Cenik::DM_ROCNI_CLENSTVI ||
							(($this->castka == Cenik::ROCNI_CLENSTVI+Cenik::ZPOZDNE) || ($this->castka == Cenik::DM_ROCNI_CLENSTVI+Cenik::ZPOZDNE))))
                ||  ( $this->is_late_fee() AND ($this->castka == Cenik::ROCNI_CLENSTVI+Cenik::ZPOZDNE OR $this->castka == Cenik::DM_ROCNI_CLENSTVI+Cenik::ZPOZDNE))
				||  ($this->castka == Cenik::ROCNI_DOPORUCENE)
            )   AND (mb_strlen($vs_string) <= 5))
        {
            $this->typ = "clenske_jednotlivec";
            $this->typ_podrobny = "příspěvek čl.č. {$vs_string}";

            // nepoužívat hledací rutinu, tady hledat přímo
            // v databázi následně najdeme zda existuje dané členské číslo a doplníme do pole
            // může jich být více
            $this->relevantni_cisla[] = $vs_string;

            // platbu s pokutou neumime zpracovat automaticky
            if ($this->is_late_fee()){
                $r = date ('Y', $this->datum_uctovani);
                // nastav klasifikaci, jen pokud jeste neni
                if($this->castka == Cenik::ROCNI_CLENSTVI+Cenik::ZPOZDNE && $this->klasifikace == '') $this->klasifikace = "{$r}"; // kod pro uctovani
                // 2018-04-10: platby amtomaticky nekategorizovat, vedlo to k tomu, ze to nelze zmenit
                // tady je totiz vice moznosti a neni jasne co to je, radeji se dela rucne
                // if($this->castka == 400) $this->klasifikace = "{$r}dm+100@pokuta"; // kod pro uctovani
            }
            return TRUE;
        }



        // zápisné pololetní: 150+150, 300+150, celé: 300+150, 600+150
        // datum je číslo, ale první nula může být useknutá
        // 2017-07-27, TK: casto se stava, ze zapisne poslou jako samostatnych 150
        // 2018–08–15, AN: Na zadost Zuzky odstranena castka 150, protoze zpusobuje parovani plateb,
        // ktere nejsou vstupem noveho clena
        if (($this->castka == Cenik::DM_PULROCNI_CLENSTVI+Cenik::ZAPISNE ||
				$this->castka == Cenik::PULROCNI_CLENSTVI+Cenik::ZAPISNE ||
				$this->castka == Cenik::DM_ROCNI_CLENSTVI+Cenik::ZAPISNE ||
				$this->castka == Cenik::ROCNI_CLENSTVI+Cenik::ZAPISNE)
            AND (mb_strlen($vs_string) >= 7 AND mb_strlen($vs_string) <= 8))
        {

            // datum ma byt osmimistne, ale nekdy se usekne prvni nula, doplne pred porovnanim
            // automaitcky dekodovane datum vlozi na seznam potencialnich dat
            if ($this->decode_dates($vs_string))
            {
                $this->typ = "vstup_jednotlivec";
                $this->typ_podrobny = "nový člen";
                return TRUE;
                // pokud neprošla analýza data, pokračuj dále, platbu jsme nepoznali
            }
        }



        // platby za IQ testy
        // od 7/2017 jsou ceny testu 150, 200 a 300 Kc
        // bohuzel castka 150 a 300 Kc se kryje s prispevkem, takze se sem nikdy nedojde.
        // to nastesti nevadi, udajne jich chodi opravdu malo
        if (($this->castka == Cenik::TEST_DOSPELI || $this->castka == Cenik::TEST_DETI)
            AND (mb_strlen($vs_string) >= 7))
        {
            $this->typ = "iq_test";
            $this->typ_podrobny = "platba IQ testu";

            // nelze párovat, přihlášky se totiž musí porovnávat vůči jiné tabulce :(
            return TRUE;
        }


        // 2017-07-27, TK: 100 Kc je vzdycky certifikat
        if ($this->castka == Cenik::CERTIFIKAT)
        {
            $this->typ = "certifikat";
            $this->typ_podrobny = "certifikát";
            if ($this->klasifikace == '') $this->klasifikace = "+1C"; // kod pro uctovani

            // primárně by tam mělo být datum narození - dle instrukcí
            $this->decode_dates($this->detail);  // na data
            $this->decode_dates($vs_string);    // na data
            return TRUE;
        }




        /////////////////////////////////////////
        // i pro neznámé platby připárujeme záznam dle clc ve VS, pokud je
        $this->decode_clc($vs_string);

        // i pro neznámé platby projedeme poznámku, zda tam nejosu členská čísla, často bývají
        $this->decode_clc($this->detail);

        // ověř zda VS není datum, konverze na string by mela probehnout automaticky
        $this->decode_dates($vs_string);

        // ověř, zda není datum v textovém popisu platby
        // funkce potenciální data rovnou uloží
        $this->decode_dates($this->detail);

        // ať už jsme něco našli nebo ne, jisti si nejsme
        $this->typ = "neidentifikovana";
        $this->typ_podrobny = "nerozpoznaná transakce";
        return FALSE;
    }


    /**
     * Prověří, zda k nalezeným členským číslům a datům
     * existují v databázi potenciální protikusy.
     *
     * Pozor, mapování není zdaleka 1:1, protže se čl. č. ukládají bez dm,
     * může být k jednomu čl. č. 0, 1 nebo 2 záznamy.
     *
     * U dat narození to je ještě variabilnější.
     *
     * Vyžaduje databázový objekt a kryptovací objekt.
     *
     * @param $db: db. objekt
     * @param $aes_crypt: sifrovaci objekt pro pristup k polim IQ
     */
    private function pair($db, $aes_crypt)
    {
        global $DATABASE;

        foreach ($this->relevantni_cisla as $cislo)
        {
            // v db. jsou členská čísla uložena jako 4 místný string !!! při srovnání je potřeba tedy upravit
            if ($cislo < 1000)
                $cislo = sprintf('%04d', $cislo);

            // dotaz se databaze
            // pocitame, ze dostavame pouze nami pripravena data, tak tu neni dalsi kontrola na SQL injection
            $vysledek = $db->Query("SELECT c_id_m, typ, jmeno, prijmeni FROM {$DATABASE}.c_m_members WHERE clencislo = '{$cislo}'");

            while ($radek = $db->FetchAssoc($vysledek)) {
                // rozlišeuj děti a dospělé
                if ($radek['typ'] == 2)
                    $this->relevantni_zaznamy[$radek['c_id_m']] = "{$radek['jmeno']}&nbsp;{$radek['prijmeni']}&nbsp;(dm{$cislo})";
                else
                    $this->relevantni_zaznamy[$radek['c_id_m']] = "{$radek['jmeno']}&nbsp;{$radek['prijmeni']}&nbsp;({$cislo})";
            }
        }

        // resetuje relevantni cisla
        $this->relevantni_cisla = Array();


        // spočti rok, dle kterého odsekneme testy
        $cutoff_year = date("Y") - 3;
        foreach ($this->relevantni_data as $datum)
        {
            // dotaz se databaze
            // pocitame, ze dostavame pouze nami pripravena data, tak tu neni dalsi kontrola na SQL injection
            // dat narozeni tolik není, proto při párování bereme jen testované za poslední 2 roky
            $vysledek = $db->Query("SELECT
                    c_id_m, typ, jmeno, prijmeni, clencislo, iq1, iq2, iq3, per1, per2, per3, mitch_iq_1, mitch_iq_2, mitch_iq_3
                FROM
                    {$DATABASE}.c_m_members
                WHERE
                    datumnar = '{$datum}'
                    AND ((YEAR(datumtestu1) >= {$cutoff_year}) OR (YEAR(datumtestu2) >= {$cutoff_year}) OR (YEAR(datumtestu3) >= {$cutoff_year}) OR (YEAR(mitch_datum_1) >= {$cutoff_year}) OR (YEAR(mitch_datum_2) >= {$cutoff_year}) OR (YEAR(mitch_datum_3) >= {$cutoff_year}))");

            while ($radek = $db->FetchAssoc($vysledek)) {
                // dekóduj IQ a nepokračuj dál, pokud IQ není dostatečné
                // rozlišeuj děti a dospělé
                if ($radek['typ'] == 2)
                {
                    // pro test nepouzivej kryptovani
                    if ($DATABASE == "test")
                    {
                        $per1 = (int) ($radek['per1']);
                        $per2 = (int) ($radek['per2']);
                        $per3 = (int) ($radek['per3']);
                        $iq1 = intval(preg_replace('/[^0-9]/', '', ($radek['mitch_iq_1'])));
                        $iq2 = intval(preg_replace('/[^0-9]/', '', ($radek['mitch_iq_2'])));
                        $iq3 = intval(preg_replace('/[^0-9]/', '', ($radek['mitch_iq_3'])));
                    }
                    else
                    {
                        $per1 = (int) ($aes_crypt->decrypt($radek['per1']));
                        $per2 = (int) ($aes_crypt->decrypt($radek['per2']));
                        $per3 = (int) ($aes_crypt->decrypt($radek['per3']));
                        $iq1 = intval(preg_replace('/[^0-9]/', '', ($aes_crypt->decrypt($radek['mitch_iq_1']))));
                        $iq2 = intval(preg_replace('/[^0-9]/', '', ($aes_crypt->decrypt($radek['mitch_iq_2']))));
                        $iq3 = intval(preg_replace('/[^0-9]/', '', ($aes_crypt->decrypt($radek['mitch_iq_3']))));
                    }


                    // v per budou nuly pro žádné hodnoty, tj. provnání funguje
                    if ($per1 < 99 AND $per2 < 99 AND $per3 < 99 AND $iq1 < 130 AND $iq2 < 130 AND $iq3 < 130)
                        continue;

                    if (trim($radek['clencislo']) == '')
                        $radek['clencislo'] = ' nečlen';

                    // dekóduj
                    $this->relevantni_zaznamy[$radek['c_id_m']] =
                        "{$radek['jmeno']}&nbsp;{$radek['prijmeni']}&nbsp;(dm{$radek['clencislo']})&nbsp;{$datum}";
                }
                else
                {

                    // pro test nepouzivej kryptovani
                    if ($DATABASE == "test")
                    {
                        $iq1 = (int) ($radek['iq1']);
                        $iq2 = (int) ($radek['iq2']);
                        $iq3 = (int) ($radek['iq3']);
                    } else
                    {
                        $iq1 = (int) ($aes_crypt->decrypt($radek['iq1']));
                        $iq2 = (int) ($aes_crypt->decrypt($radek['iq2']));
                        $iq3 = (int) ($aes_crypt->decrypt($radek['iq3']));
                    }


                    if (trim($radek['clencislo']) == '')
                        $radek['clencislo'] = 'nečlen';

                    if ($iq1 >= 130 OR $iq2 >= 130 OR $iq3 >= 130)
                        $this->relevantni_zaznamy[$radek['c_id_m']] =
                            "{$radek['jmeno']}&nbsp;{$radek['prijmeni']}&nbsp;({$radek['clencislo']})&nbsp;{$datum}";
                }
            }
        }

        // resetuj
        $this->relevantni_data = Array();
    }


    /**
     * Načte platbu z db. na základě reference_banky, což je unikátní
     * identifikátor. Kvůli následnému párování vyžaduje db. a crypto objekt.
     *
     * @param string $reference_banky reference banky, PK pro uložení
     * @param databaseObject $db (centrální databáze)
     * @param aes_crypt $aes_crypt
     * @param null|bool $valid validovat data ?
     */
    function __construct($reference_banky, $db, $aes_crypt, $validation = null)
    {
        global $DATABASE;

        if (is_null($validation)) {
            $validation = true;
        }

        // pro jistotu (pokud totiž selže další část konstruktoru
        // nemáme to jak poznat
        $this->valid = FALSE;

        // má smysl pokračovat?
        if ($reference_banky == NULL OR mb_strlen(trim($reference_banky)) < 5)
        {
            echo "<li>Chtěl jsem načíst transakci z databáze, ale nedostal jsem referenci banky.</li>";
            return FALSE;
        }

        // v testu je ok nedostat crypt
        if ($db == null OR ($aes_crypt == NULL AND $DATABASE != "test"))
        {
            echo "<li>Chtěl jsem načíst transakci {$reference_banky}, ale nedostal jsem odkaz na databázi nebo krypto modul.</li>";
            return FALSE;
        }

        // sanitizuj referenci banky
        $reference_banky = $db->getEscapedString($reference_banky);

        // získej co vylezlo
        $result = $db->Query("SELECT * FROM {$DATABASE}.c_m_transakce WHERE reference_banky = '{$reference_banky}'");

        // něco je strašně špatně !!!
        if ($result == FALSE)
        {
            echo "<li>Chtěl jsem načíst transakci {$reference_banky}, ale dotaz do db. selhal, result == FALSE.</li>";
            return FALSE;
        }

        if ($db->getNumRows($result) != 1)
        {
            echo "<li>Chtěl jsem načíst transakci {$reference_banky}, ale dotaz do db. vrátil 0 řádek, result: {$result}, rows: {$db->getNumRows($result)}.</li>";
            return FALSE;
        }

        // pokud máme jednu řádku, stáhni jí
        $result = $db->FetchAssoc($result);


        // zavolej původní konstruktor, který znovu provede parsování řádky CSV (uložené v db.) a podle toho nastaví hodnoty polí
        // smozřejmě ho nenechaj nic ukládat !!!
        parent::__construct($result['radek_transakce']);

        // něco je špatně, taková platba neměla být v db.
        if (!$this->valid)
        {
            echo "<li>Načetl jsem z db. transakci {$reference_banky}: {$result['castka']} Kč {$result['datum_uctovani']}, ale selhalo zpracování údajů o transakci.</li>";
            return FALSE;
        }

        // doplň nové informace
        $this->stav = $result['stav'];
        $this->poznamka = $result['uzivatelska_poznamka'];
        $this->klasifikace = $result['uzivatelska_klasifikace'];
        // souvisejici_zaznamy
        // prověř, zda souhlasí db. s tím co bylo rozpoznáno
        if (($result['castka'] != $this->castka OR $result['variabilni_symbol'] != $this->vs) && $validation)
        {
            echo "<li>U transakce {$reference_banky} nesedí částka a vs v db. s tím co jsem dekódval.</li>";
        }


        // validní platbu rovnou klasifikuj (navratova hodnota funkce se sice uloží, ale význam nemá, dále se nevyužije)
        $this->classified = $this->classify();
        $this->pair($db, $aes_crypt);
    }



    /**
     * print_table
     * Sofistikovanější tabulkový výpis celé tabulkové řádky
     * Záhlaví lze tisknout následující statickou metodou.
     *
     * @param string $tr_element_classes  obsahuje html vlastnosti elementu <tr>, které mají být použity
     * @param bool $edit        ma se vypsat editacni policko
     * @param bool $souvisejici maji se vypsat souvisejici zaznamy
     * @param $tab_poznamka     cislo tab stopu, se kterym se objevi poznamka
     * @return string   HTML kód řádky
     */
    public function print_table($tr_element_classes, $edit = FALSE, $souvisejici = TRUE, $tab_poznamka = 0)
    {
        if (!$this->valid)
            return "<tr><td>Nezpracovatelná platba</td></tr>\n";

        // urči barvu
        $color = Transakce_z_databaze::get_color($this->get_type());



        // zjisti, zda máme související záznamy
        $linky = Array();
        foreach ($this->relevantni_zaznamy as $c_id_m => $text)
            $linky[] = " <a href='./index.php?men=men19.2.2.0&c_id_m={$c_id_m}&transakce=" . urlencode($this->get_reference_banky()) . "'
                target='_blank' style='color: {$color};'>{$text}</a>";
        $linky = implode(', &nbsp; ', $linky);




        // výpis datové řádky
        echo "<tr class='{$tr_element_classes}' style='color: {$color};'>\n";
        echo $this->print_s('table');
        echo "</tr>";

        // vypis souvisejici zaznamy jen pokud jsou nalezene a je to povolene
        if (count($this->relevantni_zaznamy) > 0 AND $souvisejici)
            echo "<tr class='{$tr_element_classes}' style='color: {$color};'>
                  <td colspan='8'>Související záznamy: {$linky}</td></tr>\n";





        // vypiš editační políčka
        if ($edit)
        {

            // připrav poznámku na vložení do formuláře
            $this->poznamka = htmlspecialchars($this->poznamka, ENT_QUOTES);


            echo "<tr class='{$tr_element_classes}' style='color: {$color};'><td colspan='8'>";


            // interní informacep otřebné pro ukládání
            // nejprve se uloží reference, podle které se bude dohledávat zbytek polí
            echo "
            <input type='hidden' id='transakce_{$this->reference_banky}' name='transakce_{$this->reference_banky}'
                value='{$this->reference_banky}'>

            <input type='hidden' id='t_{$this->reference_banky}_puvodni_stav'        name='t_{$this->reference_banky}_puvodni_stav'
                value='{$this->stav}'>

            <input type='hidden' id='t_{$this->reference_banky}_puvodni_poznamka'    name='t_{$this->reference_banky}_puvodni_poznamka'
                value='{$this->poznamka}'>

            <input type='hidden' id='t_{$this->reference_banky}_puvodni_klasifikace' name='t_{$this->reference_banky}_puvodni_klasifikace'
                value='{$this->klasifikace}'>

            ";



            echo "
            <input  id='t_{$this->reference_banky}_stav_nova' name='t_{$this->reference_banky}_stav' type='radio' " .
            (($this->stav == 'nova') ? " checked='checked'" : "") . " value='nova'>
            <label for='t_{$this->reference_banky}_stav_nova'>nová</label>

            &nbsp;

            <input  id='t_{$this->reference_banky}_stav_ceka' name='t_{$this->reference_banky}_stav' type='radio' " .
            (($this->stav == 'ceka') ? " checked='checked'" : "") . " value='ceka'>
            <label for='t_{$this->reference_banky}_stav_ceka'>čeká</label>

            &nbsp;

            <input  id='t_{$this->reference_banky}_klasifikace' name='t_{$this->reference_banky}_klasifikace' type='text' size='30'
                value='{$this->klasifikace}'/>

            &nbsp;

            <input  id='t_{$this->reference_banky}_poznamka' name='t_{$this->reference_banky}_poznamka' type='text' size='40'
                value='{$this->poznamka}'/>

            <input  id='t_{$this->reference_banky}_stav_hoto' name='t_{$this->reference_banky}_stav' type='radio' ".
            (($this->stav == 'zpracovana') ? " checked='checked'" : "") . " value='zpracovana' >
            <label for='t_{$this->reference_banky}_stav_hoto'>zpracovaná</label>

            &nbsp;
            &nbsp;
            &nbsp;
            &nbsp;
            &nbsp;

            <input type='submit' id='t_{$this->reference_banky}_submit' name='uloz'  value='Uložit změny'/>
            ";


            echo "</td></tr>\n\n\n";
        }


        // zobraz volnou radku jen pokdu se zobrazuji souvisejici platby
        if ($souvisejici)
            echo "<tr class='{$tr_element_classes}' style='color: {$color};'><td colspan='8'>&nbsp;</td></tr>\n\n\n";
    }



    /**
     * seznam_moznych_id
     * Vrati seznam vsech id, ke kterym platba muze platit
     * @return array pole id
     */
    public function seznam_moznych_id()
    {
        //printf("pocet zaznamu: %d, pocet datumu: %d, pocet cisel: %d<br>\n", count($this->relevantni_zaznamy), count($this->relevantni_data), count($this->relevantni_cisla));
        return $this->relevantni_zaznamy;
    }



    /**
      Vezme data z formuláře a pokusí se uložit transakci.

      Jedná se o statickou metodu, protože v době odeslání stránky bohužel
      původní objekt již neexistuje.

      Dostane referenci banky a očekává, že podle ní najde přímo v POST
      odpovídající políčka
     */
    public static function ulozit_transakci_z_formulare($reference_banky, $db)
    {
        global $DATABASE;

        // podle přítomnosti submitu ověř, že vše bylo zasláno
        if (mb_strlen(trim($reference_banky)) < 5)
        {
            echo "<li>Byla volána funkce uložení transakce, ale nedostal jsem referenci banky.</li>";
            return FALSE;
        }

        // podle přítomnosti submitu ověř, že vše bylo zasláno
        /*
          if (!isset($_POST["t_{$reference_banky}_submit"]))
          {
          echo "<li>Transakci {$reference_banky} jsem neuložil, nedostal jsem všechna data.</li>";
          return FALSE;
          } */

        // načti všechny vstupy, odstraň HTML opravy
        $puvodni_poznamka = htmlspecialchars_decode($_POST["t_{$reference_banky}_puvodni_poznamka"], ENT_QUOTES);
        $puvodni_klasifikace = htmlspecialchars_decode($_POST["t_{$reference_banky}_puvodni_klasifikace"], ENT_QUOTES);
        $puvodni_stav = $_POST["t_{$reference_banky}_puvodni_stav"];

        $nova_poznamka = htmlspecialchars_decode($_POST["t_{$reference_banky}_poznamka"], ENT_QUOTES);
        $nova_klasifikace = htmlspecialchars_decode($_POST["t_{$reference_banky}_klasifikace"], ENT_QUOTES);
        $novy_stav = $_POST["t_{$reference_banky}_stav"];
        
        if (!isset($_POST["t_{$reference_banky}_stav"])) {
          // data z formulare nejsou kompletni -- nic nedelej
          return FALSE;
        }


        // jestli se nic nezměnilo, nepokračuj dál
        if ($puvodni_stav == $novy_stav
            AND $puvodni_klasifikace == $nova_klasifikace
            AND $puvodni_poznamka == $nova_poznamka)
        {
            // transakce se nezměnila
            return FALSE;
        }

        // nejprve ošetři referenci banky
        $reference_banky = $db->getEscapedString($reference_banky);

        $sql_cast = '';
        if ($puvodni_stav != $novy_stav)
        {
            $novy_stav = $db->getEscapedString($novy_stav);
            $sql_cast .= " stav = '{$novy_stav}'";
        }

        if ($puvodni_klasifikace != $nova_klasifikace)
        {
            $nova_klasifikace = $db->getEscapedString($nova_klasifikace);
            $sql_cast .= (($sql_cast != '') ? ', ' : '') . " uzivatelska_klasifikace = '{$nova_klasifikace}' ";
        }

        if ($puvodni_poznamka != $nova_poznamka)
        {
            $nova_poznamka = $db->getEscapedString($nova_poznamka);
            $sql_cast .= (($sql_cast != '') ? ', ' : '') . " uzivatelska_poznamka = '{$nova_poznamka}' ";
        }

        // pokud se něco změnilo, ulož to do databáze
        $sql_zaklad = "UPDATE {$DATABASE}.c_m_transakce SET {$sql_cast} WHERE reference_banky = '{$reference_banky}' LIMIT 1";
        // echo "<li>query: $sql_zaklad</li>";

        // return true if the doQuery returned true
        $result = $db->Query($sql_zaklad);
        // echo "<li>result: {$result}</li>";

        if ($result === TRUE){
            $rows = $db->getAffectedRows();
            // echo "<li>{$reference_banky}: result: {$result}, rows: {$rows}</li>";
            return $rows;
        }
        else {
            return FALSE;
        }
    }



    public function uloz_zpracovano($db, $id_m, $uzivKalsifikace = false)
    {
        global $DATABASE;
        if ($uzivKalsifikace == false)
            $SQL = "update {$DATABASE}.c_m_transakce set stav='zpracovana', editoval='{$id_m}' where reference_banky = '{$this->reference_banky}' limit 1";
        else
            $SQL = "update {$DATABASE}.c_m_transakce set stav='zpracovana', editoval='{$id_m}', uzivatelska_klasifikace='$uzivKalsifikace' where reference_banky = '{$this->reference_banky}' limit 1";
        $db->Query($SQL);
    }



    public function uloz_testovani($db, $id_m)
    {
        global $DATABASE;
        $SQL = "update {$DATABASE}.c_m_transakce set uzivatelska_klasifikace='test', stav='zpracovana', editoval='{$id_m}' where reference_banky = '{$this->reference_banky}' limit 1";
        $db->Query($SQL);
    }



    public function uloz_uziv_klas($db, $id_m, $uziv_klas)
    {
        global $DATABASE;
        $SQL = "update {$DATABASE}.c_m_transakce set uzivatelska_klasifikace='{$uziv_klas}', stav='zpracovana', editoval='{$id_m}' where reference_banky = '{$this->reference_banky}' limit 1";
        $db->Query($SQL);
    }



    /**
      Vytiskne řádku tabulky se záhlavím.
      (pro tabulkový výpis plateb).

      statiká metoda
     */
    public static function print_table_head()
    {
        echo '<tr>
                  <th>Typ</th>
                  <th>Datum</th>
                  <th>Poř.</th>
                  <th>Částka</th>
                  <th>Číslo účtu</th>
                  <th>Plátce</th>
                  <th>VS</th>
                  <th>Popis</th>
             </tr>';
    }


    /**
     * Vrací array s hodnotama 
     */
    public function toArray() 
    {
        return [
               'typ' => (isset($this->klasifikace) ? $this->klasifikace : null),
               'datum' => (isset($this->datum_uctovani) ? $this->datum_uctovani : null),
               'vypis' => (isset($this->vypis) ? $this->vypis : null),          
               'castka' => (isset($this->castka) ? $this->castka : null),
               'ucet' => (isset($this->ucet_protistrany) ? $this->ucet_protistrany : null),
               'nazev' => (isset($this->nazev_protistrany) ? $this->nazev_protistrany : null),
               'vs' => (isset($this->vs) ? $this->vs : null),
               'detail' => (isset($this->detail) ? $this->detail : null),
        ];
    }


    /**
     * Ulozi VSECHNY zaznamy odeslane pres POST.
     *
     * @param $cdb: (ukazatel cdb.)
     * @return int vrati pocet ulozenych zaznamu
     */
    public static function ulozInformacePlatbach($cdb)
    {
        global $_POST;
        //echo 'aaaaaaaaaaaaaaaaa';

        // nepouzivej
        if (!isset($_POST['uloz']))
            return 0;


        echo '<h3>Ukládám uživatelské změny</h3>';
        //print_r($_POST);
        // najdi klice ke vsem transakcim
        $klice = array_keys($_POST);
        $transakce = Array();
        foreach ($klice as $klic)
            if (mb_strrpos($klic, 'transakce_') === 0)
                $transakce[] = $_POST[$klic];
        echo "<p>Na stránce bylo " . count($transakce) . " transakcí.</p>";

        // zavolej ulozeni na kazdou nalezenou transakci
        $pocet = 0;
        foreach ($transakce as $t)
        {
            $pocet += Transakce_z_databaze::ulozit_transakci_z_formulare($t, $cdb);
        }
        echo "<p>Uložil jsem {$pocet} změněných transakcí.</p>";
        // porkacuje se dal, coz znamena vypis nezpracovanych transakci

        return $pocet;
    }

}
