<?php
/**
 * <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
 *
 * detail.i
 * Centrální databáze – editace záznamu (nová verze)
 * Karta úprav záznamu v centrální databázi
 * verze 2012, přepsal: <PERSON><PERSON><PERSON> Feb-2012
 *
 * Přístupy na tuto stránku jsou zaznamenávány.
 *
 * Poslední změny
 * 2014-09-30, VK: přidána obsluha tla´čítek pro tisk výsledků testování
 * 2014-01-28, TK: zapsani autplatby certifikatu
 * 2013-02-20, TK: pretty print, oprava chyb, clenska cisla
 * 2012-12-30, TK: update zpracování plateb
 * 2012-10-10, TK: zpracovani plateb
 * 2012-07-24, TK: přeformátování
 * 2012-07-19, TK: deaktivace průkazů
 * 2012-06-06, TK: upraven komentář v poznámce, doplněna proměnná pro kontrolu obnovení členství při ukládání
 * 2012-03-08, TK: finalizováno
 * 2012-02-29, TK: změna způsobu určení podbarvení
 * 2012-02-28, TK: založeno
 */

use \crypto\CryptoFactory;
use \mailer\MailerInterface;
use \mailer\MailerFactory;

use intranet\platby\ucet\Transakce_z_databaze;
use Mpdf\Mpdf;

require_once('platby-cenik.class.php');

// vstupní bezpečnostní kontroly
// db. je v intranetu přístupná automaticky
if (!access("read", $men, $a_user["id_m"], $db))
    die("<h3 style='color: red;'>K prohlížení této stránky nemáte oprvánění!</h3>");

// data předaná jako GET
if (!isset($c_id_m) and !isset($n_id_m))
    die("<h3 style='color: red;'>Nedostal jsem platný identifikátor člena.</h3>");

// $c_id_m je nastaveno jako get promenna, je treba overit
if (!is_numeric($c_id_m) and !is_numeric($n_id_m))
    die("<h3 style='color: red;'>Identifikátor člena není platné číslo.</h3>");
// korekce vstupu
$c_id_m = isset($c_id_m) ? intval($c_id_m) : -1;
$n_id_m = isset($n_id_m) ? intval($n_id_m) : -1;

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// Globals
// zpracovani plateb
require_once('../ssl_pages_new/centr/sekretarka/platby-ucet.class.php');

// pristup k databazi
require_once("../ssl_library_new/gen_kod.l");

$aes_crypt = CryptoFactory::getAES();

// nemusi byt nastavene, naopak, podle nenastaveni se pozna, ze nic
// $new_certifikat = "";


// konstanta pro identifikaci typu testu pro tisk vysledku
DEFINE('TYP_TESTU_RAVEN', 'RAVEN');
DEFINE('TYP_TESTU_MITCH', 'MITCH');


////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// blok pro tisk výsledků testů - resi tato stranka, ale po zpracovani je dalsi parsovani ukonceno
require_once("../ssl_pages_new/centr/testujici/zpracovani_testovani_class.php");

$valid_raven_actions = array(
    "TiskRaven1",
    "TiskRaven2",
    "TiskRaven3",
    "EmailRaven1",
    "EmailRaven2",
    "EmailRaven3"
);
$valid_mitch_actions = array(
    "TiskMitch1",
    "TiskMitch2",
    "TiskMitch3",
    "EmailMitch1",
    "EmailMitch2",
    "EmailMitch3"
);
$valid_other_actions = array(
    "ConfirmEmail"
);

// undefined variable mensasec
$mensasec = "mensasec";

if (isset($_POST['akce']) && $_POST['akce'] == "ConfirmEmail") {
    require_once '../ssl_pages_new/auto_email2/config.i';

    // kam jde email - nacist z poslanych dat, aby se sohdoval s tim, ktery uvidi uzivatel.
    $target_email = $_POST['email'];

    $odkaz = "https://" . $_SERVER['SERVER_NAME'] . "/prihlaska/confirm_email_prihlaska.php?email=$target_email&id_m=" . $_POST['id_m'];

    // TODO do šablony
    $mailbody = "
        <p>
            Vážená/ý paní/pane,<br /><br />

            Nabízíme vám možnost získat výsledek testu IQ v elektronické podobě.<br /><br />

            Potvrzením odkazu {$odkaz} souhlasíte se zasláním výsledku testu IQ na e-mail.<br /><br />

            Děkuji vám za váš zájem,<br />
            s pozdravem,<br />
            Mensa Česko<br />
        </p>
    ";

    $is_sent = MailerFactory::getMailer()->sendDefaultMail(
        MailerInterface::NOREPLY_MAIL_ADDRESS,
        "Mensa Česko",
        [
            $target_email => $target_email,
        ],
        "Potvrzení e-mailu",
        $mailbody
    );

    if (strlen($target_email) > 0 && $is_sent) {
        echo "<h3>Potvrzovací e-mail byl odeslán na {$target_email}.</h3>";
        exit;
    } else {
        echo "<h3>Potvrzovací e-mail <em>nebyl</em> odeslán.</h3>";
        exit;
    }
}

if (isset($_POST['akce']) && in_array($_POST['akce'], array_merge($valid_raven_actions, $valid_mitch_actions, $valid_other_actions))) {
    $testovani = new zpracovani_testovani($db2, $mensasec, $a_user, $men);
    $cislo_testu = 0;
    $typ_testu = null;

    switch ($_POST['akce']) {
        case 'TiskRaven1':
            $cislo_testu = 1;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_RAVEN;
            break;
        case 'TiskRaven2':
            $cislo_testu = 2;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_RAVEN;
            break;
        case 'TiskRaven3':
            $cislo_testu = 3;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_RAVEN;
            break;

        case 'EmailRaven1':
            $cislo_testu = 1;
            $vyber_akci = 'Email';
            $testyAtt = $att1;
            $typ_testu = TYP_TESTU_RAVEN;
            break;
        case 'EmailRaven2':
            $cislo_testu = 2;
            $vyber_akci = 'Email';
            $testyAtt = $att2;
            $typ_testu = TYP_TESTU_RAVEN;
            break;
        case 'EmailRaven3':
            $cislo_testu = 3;
            $vyber_akci = 'Email';
            $testyAtt = $att3;
            $typ_testu = TYP_TESTU_RAVEN;
            break;

        case 'TiskMitch1':
            $cislo_testu = 1;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_MITCH;
            break;
        case 'TiskMitch2':
            $cislo_testu = 2;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_MITCH;
            break;
        case 'TiskMitch3':
            $cislo_testu = 3;
            $vyber_akci = 'Tisk';
            $typ_testu = TYP_TESTU_MITCH;
            break;

        case 'EmailMitch1':
            $cislo_testu = 1;
            $vyber_akci = 'Email';
            $testyAtt = $att1;
            $typ_testu = TYP_TESTU_MITCH;
            break;
        case 'EmailMitch2':
            $cislo_testu = 2;
            $vyber_akci = 'Email';
            $testyAtt = $att2;
            $typ_testu = TYP_TESTU_MITCH;
            break;
        case 'EmailMitch3':
            $cislo_testu = 3;
            $vyber_akci = 'Email';
            $testyAtt = $att3;
            $typ_testu = TYP_TESTU_MITCH;
            break;
    }

    /* $aes_crypt je šifrovací objekt, $c_id_m je členské číslo
     * Načte data člena pro tisk
     */
    $userData = $testovani->ReadUserData($aes_crypt, $c_id_m, $cislo_testu);

    // generovani PDF je nasledujici:
    // Natáhnou se formuláře a pak natáhne soubor pro zobrazení strany
    // bez standardních hlaviček, ten se vypise do bufferu a ulozi do pdf

    // $typ clena je nastavován kdesi nahoře (jedna se o to zda je to 1 dospely nebo 2 dite).
    // nejprve zpracujeme dite
    if ($userData['typ'] == 2) {
        // normalni detsky test
        if ($typ_testu === TYP_TESTU_RAVEN) {
            // stary test pouzivany od roku 1990 - barevny Raven.
            require_once('../ssl_pages_new/centr/sekretarka/nove-otestovano.form_deti.php');

        } else if ($typ_testu === TYP_TESTU_MITCH) {
            require_once('../ssl_pages_new/centr/sekretarka/mitch-calculation.class.php');
            $mitch_calculation = new \intranet\mitch\Calculation($userData);

            // novy mensovni test
            require_once('../ssl_pages_new/centr/sekretarka/nove-otestovano.form_mitch.php');
        } else {
            die("Neočekávaný typ testu.");
        }
    } else if ($userData['typ'] == 1) {
        // vysledky pro dospeleho
        if ($typ_testu === TYP_TESTU_RAVEN) {
            require_once('../ssl_pages_new/centr/sekretarka/nove-otestovano.form_dospeli.php');
        } else if ($typ_testu === TYP_TESTU_MITCH) {
            die("Neočekávaný typ testu. Mitch test je určen pouze pro děti.");
        } else {
            // dospely ma zatim jen ravena cokoliv jineho je spatne
            die("Neočekávaný typ testu.");
        }
    } else {
        // typ clena je 1 dospely, 2 dite, komukoliv jinemu nemame tisknout test
        die("Necekany typ clena");
    }


    require_once("../ssl_library_new/get_content_file.l");
    ini_set('display_errors', 0);

    $mpdf = new Mpdf();
    $mpdf->useAdobeCJK = true;
    $mpdf->SetDisplayMode('fullpage');
    $mpdf->SetAuthor('Mensa Česko');
    $mpdf->WriteHTML($obsahPdf);
    $mpdf->ResetMargins(); // resets margins to defaults, not really well documented
    $mpdf->SetLeftMargin(10);
    ob_clean();

    // tisk PDF
    if (isset($vyber_akci) && $vyber_akci == 'Tisk') {
        $mpdf->Output();
        require_once('../ssl_library_2013/c_m_logy_members_class.php');

        // zapis zaznam o tisku do logu
        $datum_testu = $typ_testu === TYP_TESTU_RAVEN ? $userData['datum_testu'] : $userData['mitch_datum'];
        $logy = new c_m_logy_members($db2);
        $logy->zapisLog(1, $a_user['id_m'], $c_id_m, "Tisk testu {$typ_testu} {$cislo_testu} ze dne {$datum_testu}.");

        exit;

        // email
    } else if (isset($vyber_akci) && $vyber_akci == 'Email') {
        require_once '../ssl_pages_new/auto_email2/config.i';

        // kam jde email - nacist z poslanych dat, aby se sohdoval s tim, ktery uvidi uzivatel.
        $target_email = @$_POST['target_email'];

        // TODO do šablony
        $mail_body_mitch = "
            <p>
                Vážená paní, vážený pane,<br /><br />
                v příloze Vám zasíláme výsledek IQ testu Vašeho dítěte.<br /><br />

                Mensa Česko
            </p>
        ";

        $mail_body_raven = "
            <p>
                Vážená paní, vážený pane,<br /><br />
                jménem Mensy Vám děkujeme za absolvování IQ testu a v příloze Vám zasíláme Váš výsledek.<br /><br />

                Mensa Česko
            </p>
        ";

        // staticke prilohy - gratulace, pozvanka do Mensy atd.
        $attachments = [];
        foreach ($testyAtt as $soubor) {
            // Parametry: $file_path (odkud soubor nacte) => $file_name (jak se ma v mailu jmenovat)
            $attachments[$soubor] = basename($soubor);
        }

        // odeslani emailu s vysledkem
        // Generate PDF content
        $pdf_content = $mpdf->Output("", "S");

        // Create a mailer instance
        $mailer = MailerFactory::getMailer()->getDefaultMailer(
            MailerInterface::NOREPLY_MAIL_ADDRESS,
            "Mensa Česko",
            [
                $target_email => "{$userData['jmeno']} {$userData['prijmeni']}",
            ],
            "Výsledek testu IQ",
            $typ_testu === TYP_TESTU_MITCH ? $mail_body_mitch : $mail_body_raven,
            $attachments
        );

        // Add the PDF as a string attachment
        $mailer->addStringAttachment($pdf_content, "vysledek_testu.pdf");

        // Send the email
        $is_sent = $mailer->send();
        $mailer->clearAllRecipients();

        if ($is_sent && strlen($target_email) > 0) {
            // odeslani probehlo korektne
            echo "<html><head><meta http-equiv='content-type' content='text/html; charset=utf-8'></head>
                  <body><h3>Výsledek $cislo_testu byl odeslán na $target_email.</h3></body></html>";

            // zaloguje zaznam o poslani vysledku
            require_once('../ssl_library_2013/c_m_logy_members_class.php');
            $datum_testu = $typ_testu === TYP_TESTU_RAVEN ? $userData['datum_testu'] : $userData['mitch_datum'];
            $logy = new c_m_logy_members($db2);
            $logy->zapisLog(2, $a_user['id_m'], $c_id_m, "E-mail testu {$typ_testu} {$cislo_testu} ze dne {$datum_testu} na {$target_email}.");

            // hotovo
            exit;
        } else {
            echo "<html><head><meta http-equiv='content-type' content='text/html; charset=utf-8'></head>
                  <body><h3>Email testovanému <strong>ne</strong>byl odeslán.</h3><p>Is sent: {$is_sent}</p><p>target_email: {$target_email}</p></body></html>";
            exit;
        }
    }
}

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// pokud byla odeslana data a clen ma pravo do databaze zasahovat
if (isset($_POST['odeslat'])) {
    if (!access("update", $men, $a_user["id_m"], $db2))
        die("<h3 style='color: red;'>Nemáte právo zápisu!</h3>");

    // obsahuje kod, ktery se rovnou spusti a vykoná uložení dat
    // oddeleno pro prehlednost, vraci $c_id_m noveho zaznamu
    $c_id_m = require_once "../ssl_pages_new/centr/sekretarka/detail-zapis.i";
    echo "<p><a href=\"/index.php?men=men19.2.2.0&c_id_m=" . $c_id_m . "\">Zpět na editaci uživatele</a></p>";

    // nemíchej ukládání a editaci, nepokračuj dál
    // teoreticky by to nemělo vadit, ale nebudeme to zkoušet
    // hlavní riziko je případné již definování některých proměnných
    // původní kód totiž využívá šílenou spleť globálních
    return 0;
}

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
// priprav dotaz pro ziskani dat pro tvorbu formulare
if ($c_id_m > 0) {
    $query = "SELECT *,
        DATE_FORMAT(datumnar, '%e.%c.%Y') datumnar2,
        DATE_FORMAT(datumtestu1, '%e.%c.%Y') datumtestu12,
        DATE_FORMAT(datumtestu2, '%e.%c.%Y') datumtestu22,
        DATE_FORMAT(datumtestu3, '%e.%c.%Y') datumtestu32,
        DATE_FORMAT(zapisne_placeno, '%e.%c.%Y') zapisne_placeno2,
        DATE_FORMAT(prizpevek_placeno, '%e.%c.%Y') prizpevek_placeno2,
        DATE_FORMAT(ukonceni_clenstvi , '%e.%c.%Y') ukonceni_clenstvi2,
        DATE_FORMAT(FROM_DAYS(DATEDIFF(Curdate(), datumnar)), '%Y')  vek,
        YEAR(datumnar) rocnik, ukonceni_clenstvi_poplatky,
        DATE_FORMAT(mitch_datum_1, '%e.%c.%Y') mitch_datum_1,
        DATE_FORMAT(mitch_datum_2, '%e.%c.%Y') mitch_datum_2,
        DATE_FORMAT(mitch_datum_3, '%e.%c.%Y') mitch_datum_3

     FROM mensasec.c_m_members WHERE c_id_m = {$c_id_m}";

    $tmp = $db2->Query($query);
} else {
    // osoba neexistuje, nacti data z databaze otestovanych
    $query = "
    SELECT
        typ,
        titul,
        jmeno,
        prijmeni,
        titul_za_jmenem,
        pohlavi,
        jmeno_zz,
        per per1,
        DATE_FORMAT(datum_testu, '%e.%c.%Y') datumtestu12,
        DATE_FORMAT(datum_narozeni, '%e.%c.%Y') datumnar2,
        byt,
        ulice,
        mesto obec,
        psc,
        byt1 byt2,
        ulice1 ulice2,
        mesto1 obec2,
        psc1 psc2,
        email,
        email_status,
        email_verify_time,
        email_verify_ip,
        email2,
        email2_status,
        email2_verify_time,
        email2_verify_ip,
        telefon,
        mobil,
        iq iq1,
        certifikat,
        pocet_certifikatu,
        DATE_FORMAT(FROM_DAYS(DATEDIFF(Curdate(), datum_narozeni)), '%Y')  vek,
        YEAR(datum_narozeni) rocnik,
        mitch_odpovedi mitch_odpovedi_1,
        mitch_iq mitch_iq_1,
        /* Pozor, tady se bere sloupec DATUM_TESTU, v tabulce c_m_otestovan sloupec datum_testu_MITCH neni.
           Kazdy zaznam v tabulce totiz odpovida prave jednomu testovani a tedy prave jedno datum.
           Separatni sloupce jsou pouze pro vyslekdy, protoze Mitch ma jinou skalu hodnot. */
        DATE_FORMAT(datum_testu, '%e.%c.%Y') mitch_datum_1
    FROM
        mensasec.c_m_otestovan
    WHERE
        id={$n_id_m}";

    $tmp = $db2->Query($query);
}

///////////////////////////////////////////////////////////////////////////////
// zjisti první volné členské číslo, sjednoceno pro vsechny
// $SQL = "SELECT typ, max(clencislo) as newclencislo FROM mensasec.c_m_members GROUP BY typ";
// musi byt zkonvertovano na cislo, jinak se radi lexikograficky a 9999 je vyssi nez 10000
$cis = $db2->FetchArray($db2->Query("SELECT max(convert(clencislo, SIGNED INTEGER)) newclencislo FROM mensasec.c_m_members"));
$nove_clenske_cislo = ((int)$cis[0]) + 1;

///////////////////////////////////////////////////////////////////////////////
// ziskej data na zaklade pripraveneho dotazu (existující nebo nový záznam) ///
// pokud nejsou, nemá cenu jít dál
if ($db2->getNumRows($tmp) == 0)
    die("<h1>Nenalezen žádný existující záznam c_id_m=$c_id_m nebo nový otestovaný n_id_m=$n_id_m.</h1>");
$row = $db2->FetchArray($tmp);

// zpracuj data
$vek = (int)$row["vek"];
$row["rocnik"] = (int)$row["rocnik"];
// nastav vychozi ohdnoty
if ($c_id_m == -1) {
    $bg_color = "#FFFFFF";
    $new_certifikat = $row["certifikat"];
    $pocet_certifikatu = $row["pocet_certifikatu"];
    // musime zjistit, co to je zac a pokud to neni zadna hodnota, tak promennou zrusit
    // protoze jeji pouha existence se povazuje za indikaci toho, ze tam neco je
    if (!(strlen($new_certifikat) > 0)) {
        unset($new_certifikat);
        unset($pocet_certifikatu);
	}
    $row["clencislo"] = "";
    $row["zapisne"] = "";
    $row["zapisne_placeno2"] = "";
    $row["per2"] = "";
    $row["per3"] = "";
    $row["iq2"] = "";
    $row["iq3"] = "";
    $row["datumtestu22"] = "";
    $row["datumtestu32"] = "";
    $row["adr_pozn"] = "";
    $row["adr_pozn2"] = "";
    $row["poznamka"] = "";
    $row["rodne_prijmeni"] = "";
    $row["telefon_zam"] = "";
    $row["prizpevek"] = "";
    $row["prizpevek_placeno2"] = "";
    $row["ukonceni_clenstvi2"] = "";
    $row["ukonceni_clenstvi_poplatky"] = "0";
    $row["id_m"] = 0;
    $row["nezasilat_casopis"] = 0;
    $row["mitch_datum_2"] = "";
    $row["mitch_datum_3"] = "";
    $row["mitch_odpovedi_2"] = "";
    $row["mitch_odpovedi_3"] = "";
    $row["mitch_iq_2"] = "";
    $row["mitch_iq_3"] = "";
    $row["dm_nove_podminky"] = "";

}

// pokud je zaznam v intranetu, nacti data z intranetu
if ($row["id_m"] > 0) {
    $intra_rsq = $db2->Query("SELECT * FROM mensaweb.m_members WHERE id_m = {$row['id_m']}");
    if ($db2->getNumRows($intra_rsq) > 0)
        $intra = $db2->FetchArray($intra_rsq);
    echo "<!--";
    print_r($intra);
    echo "-->";
}

// pokud existuji priznaky, nacti dodatecne priznaky
$priznaky = Array();
if ($c_id_m > 0) {
    $priznaky_db = $db2->Query("SELECT DATE_FORMAT(datum_vstupu, '%e.%c.%Y') datum_vstupu, nevitat_v_casopise, nevitat_clenem, clencislo_v_dm
                               FROM mensasec.c_m_priznaky_clenu WHERE c_id_m = $c_id_m");
    if ($db2->getNumRows($priznaky_db) == 1)
        $priznaky = $db2->FetchArray($priznaky_db);
}

////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * nastavit podbarveni cele stranky
 *
 * Vrátí podbarvení pro stránku jako string, který se může použít v CSS
 * Rozhodování postupuje odshora a jakmile se něco vybere, dál už se nejde.
 *
 * @return: CCS kompatibilní definice barvy
 */
function vyber_barvu(&$row, $c_id_m)
{
    global $db2;
    $letos = Date("Y");

    // načti údaje o platbách (c_id_m bude nula pokud nebylo zadáno, takový záznam neexistuje)
    $zaplaceno_do = 0;
    $rs = $db2->Query("SELECT max(rok) zaplaceno_do FROM mensasec.c_m_platby WHERE c_id_m = $c_id_m");
    if ($db2->getNumRows($rs) > 0)
        $zaplaceno_do = $db2->getResult($rs, 0, "zaplaceno_do");

    // logika rozhodování: postupně vyhazovat jasné a jednoduše identifikovatelné případy
    // krok 1: nejpve Ti, co nemohou být členem v žádném případě
    // Požádal o vymazání dat: Tmavá šedá
    if ($row["ukonceni_clenstvi"] != "")
        return "#888888";

    // Požádal o vystoupení z Mensy: Světlá šedá
    // if statement musi byt rozdelene na dve faze, protoze jinak se spatne zkonvertuje retezec na pismeno
    if (($row["ukonceni_clenstvi_poplatky"] != "") and ($row["ukonceni_clenstvi_poplatky"] != "0"))
        if ($letos >= (int)$row["ukonceni_clenstvi_poplatky"])
            return "#aaaaaa";

    // dítě, které je moc staré, mělo 16. nerozeniny minulý rok nebo dříve – šedočervená
    if (($row["typ"] == 2) and ($row["rocnik"] < ($letos - 16)))
        return "#ccaaaa";

    // krok 2: lidé co nemají záznam nebo se nestali členy
    // nový záznam (nemáme klíč do cdb) - bílý
    // proměnná má nastavenu 0 (na začátku souboru), pokud nebyla zadána
    if ($c_id_m == -1)
        return "#FFFFFF";

    // nemá členské číslo, nikdy nevstoupil - žlutá (světlá)
    if ($row["clencislo"] == "")
        return "#FFFFcc";

    // má členské číslo, ale nikdy neplatil - jiná žlutá (tmavší)
    // (ale není dítě podle starých pravidel)
    if (($zaplaceno_do == 0) and !(($row["typ"] == 2) and ($row['dm_nove_podminky'] == 0))
    )
        return "#ffddaa";

    // krok 3: platný člen dle členského příspěvku
    // má zaplaceno, nestaráme se o typ, je to člen (všechny absolutní překážky členství už byly rozhodnuty)
    if ($zaplaceno_do >= $letos)
        return "#aaaaFF";

    // Dítě podle starých pravidel (dostatečně mladé) (ostatní podmínky již byly ovběřeny, tj. má věk, má členské číslo)
    if (($row["typ"] == 2) and $row['dm_nove_podminky'] == 0)
        return "#aaaaFF";

    // krok 4: různí neplatiči
    // zaplatil minulý rok - stále člen, ale musíme uhánět
    if ($zaplaceno_do == ($letos - 1))
        return "#f8adff";

    // někdy platil, ale letos nezaplaceno / cervena
    if ($zaplaceno_do < ($letos - 1))
        return "#FFaa88";

    // tak sem bychom neměli dojít
    die("<h1>Neznámý typ člena c_id_m={$c_id_m}!</h1><p>Prosím, kontaktujte administrátora a sdělte mu podrobnosti.</p>");
}

?>

<style>
    .mitch-highlight {
        padding: 5px;
        background-color: #ffcc66;
    }
</style>

<!-- *************************************************************************** -->
<!-- *************************************************************************** -->
<!--                                  Kod stranky                                -->
<!-- *************************************************************************** -->
<!-- *************************************************************************** -->
<div id="formular" class="form_detail"
     style="background-color:<?php echo vyber_barvu($row, $c_id_m); ?>; padding: 3px;">

    <?php
    // kontrola novych podminek a zobrazeni upozorneni pro cleny DM
    if ($row["typ"] == 2 AND $row['dm_nove_podminky'] == 1) {
        ?>
        <div style="width:350px; float:right; background-color:#FF3300; top:0px; padding-top:10px; padding-bottom:10px;"
             align="center">
            Člen DM podle nových podmínek, evidence neplatičů!
        </div>
        <?php
        $evidence_dm_neplaticu = true;
    } else
        $evidence_dm_neplaticu = false;
    ?>

    <!-- *************************************************************************** -->
    <!-- titulek stranky -->
    <h1>Centrální databáze – editace záznamu</h1>
    <h3>Záznam
        číslo <?php echo @$row["c_id_m"] . ": " . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row['titul_za_jmenem']) ? (", " . $row['titul_za_jmenem']) . "&nbsp;&nbsp;&nbsp;" : "") . ",     " . (($row["typ"] == 1) ? "dospělý" : (($row['dm_nove_podminky'] == 1) ? "dítě (nové podmínky)" : "dítě (staré podmínky)")); ?></h3>


    <?php
    // pokud se jedna o novohe clena, navrhni volne clenske cislo
    if ($c_id_m == -1 || $row["clencislo"] == "")
        echo "<table><tr><td width='200'><p style='color: #FF0000; font-weight: bold;'>
    Nový člen, věk: $vek<br>První volné číslo:
    <input type='text' name='nove_clenske_cislo' size='5' value='$nove_clenske_cislo'></p></td>
    <td><p>
    Všichni členové od 6. března 2012 (čl. č. 9&nbsp;343) používají jednu řadu členských čísel.
    Nezávisle na typu se dětem i dospělým přiděluje členské číslo z této řady.
    U dospělých, kteří přešli z DM číslované podle nových sjednocených čísel (čísla od cca. 9&nbsp;300) se při přechodu zachovává původní členské číslo.
    Dospělým, kteří přešli z DM dle starých čísel (do čísla cca. 2&nbsp;300) se přiděluje nové číslo. Do členského čísla dětí nevkládejte předponu dm,
    sytém to udělá automaticky.
    </p></td></tr><table>";

    // červené podbarvení políček, která by se již neměla měnit
    $zamknute_pole = "";
    if ($row["id_m"] <> "")
        $zamknute_pole = " style=\"background-color:#FF9999;\"";


    if (access("update", $men, $a_user["id_m"], $db2)) {
    ?>
    <!-- Zakzaz vicenasobne odeslani - dokument ke zpracovani pouziva data ulozena v sobe o existenci predchoziho zaznamu, dvojite zpracovani by byl pruser. -->
    <form name="form_edit" method="post" action="./index.php" onsubmit="document.getElementById('knoflik').disabled = true;
                return true;">
        <input type="hidden" name="men" value="<?php echo $men; ?>">
        <input type="hidden" name="id_m" value="<?php echo $row["id_m"]; ?>">
        <input type="hidden" name="c_id_m" value="<?php echo $c_id_m; ?>">
        <input type="hidden" name="n_id_m" value="<?php echo @$n_id_m; ?>">
        <input type="hidden" name="old_clencislo" value="<?php echo @$row["clencislo"]; ?>">
        <input type="hidden" name="old_ukonceni_clenstvi" value="<?php echo $row["ukonceni_clenstvi2"]; ?>">
        <input type="hidden" name="old_ukonceni_clenstvi_poplatky"
               value="<?php echo $row["ukonceni_clenstvi_poplatky"]; ?>">
        <input type="hidden" name="evidence_dm_neplaticu" value="<?php echo $evidence_dm_neplaticu; ?>">
        <?php
        }
        ?>

        <!-- *************************************************************************** -->
        <!-- vygeneruj tabulku zakladnich udaju -->

        <table border="0" cellpadding="2" cellspacing="0">
            <tr>
                <!-- vyber typu clena -->
                <td><select name="typ"<?php echo $zamknute_pole; ?>>
                        <option value="1"<?php if ($row["typ"] == 1) echo " selected"; ?>>Dospělá Mensa</option>
                        <option value="2"<?php if ($row["typ"] == 2) echo " selected"; ?>>Dětská Mensa</option>
                    </select>
                </td>

                <td style="color:#444400; font-weight:bold;" colspan="5">
                    <!-- zobrazeni zda je v intranetu -->

                    <?php
                    if ($row["id_m"] > 0)
                        echo "Má profil v intranetu id_m
        <a href=\"index.php?men=men3.1.4.0&id_m_m=" . $row["id_m"] . "&s_content=edit_user.i\">" . $row["id_m"] . "</a>,
        zaplacený do konce roku {$intra['prizpevky']},
        blokovaný: " . ($intra['disable'] == 'Y' ? "<span style='color:red;'>ano</span> (nemůže se přihlásit)" : "ne") . ".";
                    ?>
                </td>
            </tr>

            <!-- titulky policek v dalsi radce -->
            <tr style="font-weight:bold;">
                <td>Členské číslo</td>
                <td>Titul</td>
                <td>Příjmení</td>
                <td>Jméno</td>
                <td>Titul za jménem</td>
                <td>Pohlaví</td>
            </tr>

            <!-- policka se zakladnimi udaji z CB -->
            <tr>
                <td><input type="text" name="clencislo" value="<?php echo $row["clencislo"]; ?>"
                           size="5"<?php echo $zamknute_pole; ?>></td>
                <td><input type="text" name="titul" value="<?php echo $row["titul"]; ?>" size="7"></td>
                <td><input type="text" name="prijmeni" value="<?php echo $row["prijmeni"]; ?>" size="30"></td>
                <td><input type="text" name="jmeno" value="<?php echo $row["jmeno"]; ?>" size="30"></td>
                <td><input type="text" name="titul_za_jmenem" value="<?php echo $row["titul_za_jmenem"]; ?>" size="10"></td>
                <td rowspan="2"><input type="radio" name="pohlavi" value="1" <?php echo ($row["pohlavi"] == '1') ? "checked" : ""; ?>>Muž<br>
                <input type="radio" name="pohlavi" value="2" <?php echo ($row["pohlavi"] == '2') ? "checked" : ""; ?>>Žena</td>
            </tr>
            <!-- záznam z intranetu -->
            <tr>
                <td><?php echo (isset($intra["clen_cislo"])) ? $intra["clen_cislo"] : "&nbsp;"; ?></td>
                <td><?php echo (isset($intra["titul"])) ? $intra["titul"] : "&nbsp;"; ?></td>
                <td><?php echo (isset($intra["prijmeni"])) ? $intra["prijmeni"] : "&nbsp;"; ?></td>
                <td><?php echo (isset($intra["jmeno"])) ? $intra["jmeno"] : "&nbsp;"; ?></td>
                <td><?php echo (isset($intra["titul_za_jmenem"])) ? $intra["titul_za_jmenem"] : "&nbsp;"; ?></td>
            </tr>
        </table>
        <table border="0">

            <!-- *************************************************************************** -->
            <!--               zpracovani platby pokud je                                    -->
            <!-- *************************************************************************** -->
            <?php
            $platba = null;
            $auto_platba_rozpoznana = FALSE; // bude true, pokud jsme si jisti, že jsme platbu pochopili

            if (isset($_GET['transakce'])) {
                // zpracuj platbu
                $platba = new Transakce_z_databaze($_GET['transakce'], $db2, $aes_crypt);
                // pokud platba neni validni, zahod ji
                if (!$platba->is_valid())
                    $platba = null;
            }

            // pokud jsme platbu nasli, tak vytiskneme
            if ($platba != null) {
                // zarad infomraci o paltbe do flow tabulky
                echo '<tr><td colspan="4">
        <p style="margin-top: 1em; margin-bottom: 0;"><b>Přiřazuji následující platbu</b></p>
        <table id="transakce" style="background-color:#bbb;">';
                // záhlaví tabulky
                Transakce_z_databaze::print_table_head();
                // vytiskni info o platbe
                $platba->print_table('');
                echo '</table>';
                // nezavirej bunku celkove tabulky, budeme do ni psat info o parsovani
                ////////////////////////////////////////////////////////////////////////////
                // typ platby a castku si ulozime, protoze usnadni praci v prubehu reseni jejich hodnoty menit
                // napriklad vyresit zapisne a posleze zpracovat jako normalniho clena
                $auto_platba_typ = $platba->get_type();
                $auto_platba_castka = (int)$platba->get_castka();
                $auto_platba_mesic = (int)date('n', $platba->get_datum());

                // nyní je platba vytištěna, přirpav informace, které budou předvyplněny do tabulky
                // vime, ze mame validni platbu
                $auto_platba_rok = ($auto_platba_mesic >= 11) ?
                    ((int)date('Y', $platba->get_datum()) + 1) : date('Y', $platba->get_datum());
                $auto_platba_datum_p = date('d.m.Y', $platba->get_datum());
                $auto_platba_hodnota1 = '';
                $auto_platba_hodnota2 = '';
                $auto_platba_datum_c = date('d.m.Y', $platba->get_datum());
                $auto_platba_hodnota3 = '';
                $auto_platba_hodnota4 = '';
                $auto_platba_poznamka = "{$platba->get_cislo_uctu()} ({$platba->get_nazev_uctu()}); výpis: {$platba->get_cislo_vypisu()}; ref.: {$platba->get_reference_banky()}";

                /*
                  Logika přiřazení plateb

                  Na stránce detailů se již nekontroluje vztah platby a záznamu.
                  Pouze se automaticky vyplňují údaje o platbě dle informací z banky.
                  Informace o členovi se při porovnání nevyužívají. Vyplňované údaje se
                  také neporovnávají s již zadanými platbami.

                  Pozor, jakmile je u platby uveden rok, uloží se. Pokud rok vyplněn není
                  jakákoli další vyplněná data platby se při odeslání ztratí.

                  Pro jeden rok je možné zadat jen jednu platbu.


                  Platba nového člena

                  Pokud je platba rozpoznána jako platba nového člena, nejprve se z platby odečte zápisné
                  a zapíše se do dané kolonky. Zbytek platby se počítá jako klasická
                  členská platba dle pravidel níže.


                  Platba jednotlivce

                  Výběr roku: pokud platba přijde v listoapdu nebo později, je zvolen příští rok,
                  jinak je zvolen současný rok.

                  Výběr typu: prozkoumá se výše paltby a současné datum - měsíc. Pokud je měsíc
                  květen a později, bere se platba jako půlroční a přiřadí se jako půlroční,
                  pokud ne přiřadí se jako celá. Dle výše se určí, zda je snížená nebo plná.


                  Platba rodinná

                  Na tuto paltbu nebyla testovací data.
                 */

                // zpracuj zápisné a předej dále
                // tento if musí stát zvlášť
                if ($auto_platba_typ == 'vstup_jednotlivec') {
                    // zadat
                    // echo '<p style="margin-top: 0em; margin-bottom: 1em; color: red;">Tuto platbu nedokážu přiřadit.</p>';
                    // zaznamenje zapisne, poniz platbu a zmen typ platby
                    $auto_platba_zapisne = intranet\platby\ucet\Cenik::ZAPISNE;
                    $auto_platba_castka = $auto_platba_castka - intranet\platby\ucet\Cenik::ZAPISNE;
                    echo '<p style="margin-top: 0em; margin-bottom: 0em;">Přiřazuji zápisné ve výši '. intranet\platby\ucet\Cenik::ZAPISNE .' Kč.</p>';

                    // díky tomuto bude zbytek platby zpracován v další části
                    $auto_platba_typ = 'clenske_jednotlivec';
                    $auto_platba_zapisne_datum = $auto_platba_datum_p;
                }

                // řešení rodinného příspěvku
                if ($auto_platba_typ == 'clenske_rodina') {
                    // pokud porovnáme VS a najdeme shodu, budeme řešit jako hlavního člena
                    // jinak vyplníme platby jako u závislého člena
                    // zatím nedodělané, enbyla testovací data
                    // rozlisit, zda je zavisly nebo vedlejsi dle shody clc a vs
                    echo '<p style="margin-top: 0em; margin-bottom: 1em; color: red;">Tuto platbu rodiny nedokážu přiřadit.</p>';
                }

                // další ify jsou záměrně zřetězené
                // nastaveni castek dle typu platby
                if ($auto_platba_typ == 'clenske_jednotlivec') {
                    // dle typu clena a vyse paltby rozdel
                    // klasika dospely plny
                    // nekontroluje se měsíc, je to na celý rok
                    if ($auto_platba_castka == intranet\platby\ucet\Cenik::ROCNI_CLENSTVI) {
                        echo "<p style='margin-top: 0em; margin-bottom: 1em;'>Přiřazuji plný příspěvek na celý rok {$auto_platba_rok}.</p>";
                        $auto_platba_hodnota1 = intranet\platby\ucet\Cenik::ROCNI_CLENSTVI - intranet\platby\ucet\Cenik::CASOPIS_PULROK*2;
                        $auto_platba_hodnota3 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_hodnota4 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_rozpoznana = TRUE;
                    } // zlvevněny celý rok (pokd se platí dříve než v květnu nebo v listopadu a později - kdy se již bere další rok)
                    elseif ($auto_platba_castka == intranet\platby\ucet\Cenik::DM_ROCNI_CLENSTVI AND ($auto_platba_mesic < 5 OR $auto_platba_mesic >= 11)) {
                        echo "<p style='margin-top: 0em; margin-bottom: 1em;'>Placeno v měsíci {$auto_platba_mesic}. Přiřazuji snížený příspěvek na celý rok {$auto_platba_rok}.</p>";
                        $auto_platba_hodnota1 = intranet\platby\ucet\Cenik::DM_ROCNI_CLENSTVI - intranet\platby\ucet\Cenik::CASOPIS_PULROK*2;
                        $auto_platba_hodnota3 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_hodnota4 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_rozpoznana = TRUE;
                    } // dospeli polovicni
                    elseif ($auto_platba_castka == intranet\platby\ucet\Cenik::PULROCNI_CLENSTVI AND $auto_platba_mesic >= 5) {
                        echo "<p style='margin-top: 0em; margin-bottom: 1em;'>Placeno v měsíci {$auto_platba_mesic}. Přiřazuji plný příspěvek na půl roku {$auto_platba_rok}.</p>";
                        $auto_platba_hodnota2 = intranet\platby\ucet\Cenik::PULROCNI_CLENSTVI - intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_hodnota4 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_rozpoznana = TRUE;
                    } // zlevneny polovicni
                    elseif ($auto_platba_castka == intranet\platby\ucet\Cenik::DM_PULROCNI_CLENSTVI AND $auto_platba_mesic >= 5) {
                        echo "<p style='margin-top: 0em; margin-bottom: 1em;'>Placeno v měsíci {$auto_platba_mesic}. Přiřazuji snížený příspěvek na půl roku {$auto_platba_rok}.</p>";
                        $auto_platba_hodnota2 = intranet\platby\ucet\Cenik::DM_PULROCNI_CLENSTVI - intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_hodnota4 = intranet\platby\ucet\Cenik::CASOPIS_PULROK;
                        $auto_platba_rozpoznana = TRUE;
                    } else
                        echo "<p style='margin-top: 0em; margin-bottom: 1em; color: red;'>
                Tuto platbu jednotlivce ve výši {$auto_platba_castka} Kč nedokážu přiřadit.</p>";


                    // 100 je vzdy urcite jen certifikat, s jistotou
                } elseif ($auto_platba_castka == intranet\platby\ucet\Cenik::CERTIFIKAT) {
                    // 2014-01-25 TK: doplneni rozpoznani platby certifikatu
                    if (isset($new_certifikat))
                        echo '<p style="margin-top: 0em; margin-bottom: 0em; color: red;">Pozor, při zakládání byla předána částka certifikátu ve výši '.$new_certifikat.' Kč.</p>';

                    $new_certifikat = intranet\platby\ucet\Cenik::CERTIFIKAT;
                    $pocet_certifikatu = 1;
                    echo '<p style="margin-top: 0em; margin-bottom: 0em;">Přiřazuji platbu certifikátu ve výši '.intranet\platby\ucet\Cenik::CERTIFIKAT.' Kč.</p>';
                } // typ, ktery nenzam
                else {
                    echo '<p style="margin-top: 0em; margin-bottom: 1em; color: red;">Nevhodný typ platby.</p>';
                    // datum a poznámka budou vyplněny tak jako tak
                }


                // uzavreni bunky v hlavni tabulce
                echo '</td></tr>';
            }
            ?>

            <!-- *************************************************************************** -->
            <!--               tabulka prispevku, vlozena jako separatni tabulka             -->
            <!-- *************************************************************************** -->
            <tr>
                <td colspan="4">

                    <table border="0" cellpadding="0" cellspacing="2">
                        <!-- hlavicka tabulky -->
                        <tr>
                            <th>rok</th>
                            <th>datum<br>platby</th>
                            <th>členství<br>celý rok</th>
                            <th>členství<br>půl roku</th>
                            <th>datum<br>časopis</th>
                            <th>časopis<br>1. pololetí</th>
                            <th>časopis<br>2. pololetí</th>
                            <th>poznámka</th>
                        </tr>

                        <tr>
                            <td colspan="8">
                                <small>Speciální částky:
                                    1 – doživotní člen (vyplnit i u časopisu),
                                    2 – vázaný rodinný člen (nechat časopis prázdný, nebude chodit).<br>
                                    Platbu, u které není zadán rok systém přeskočí
                                    (pokud je smazán rok u existující platby, bude smazána celá platba).
                                </small>
                            </td>
                        </tr>

                        <!-- prvni radka, volna policka, cislovana jako 0 -->
                        <?php $i = 0; // citac na interni cislovani radku tabulky   ?>
                        <tr>
                            <!-- JS kontrola roku, musí to být tento rok nebo minulý rok, jinak je uživatel upozorněn, chybný rok je však možné nechat (záměr). -->
                            <td><input type="text" name="rok_<?php echo $i; ?>" size="4" value="<?php
                                // doplň rok platby pokud je - tady pozor, rok se doplňuje jen pokud
                                if ($auto_platba_rozpoznana)
                                    echo $auto_platba_rok;
                                ?>" onblur="var d = new Date();
        if ((this.value.length > 0) && ((this.value < d.getFullYear()) || (this.value > (d.getFullYear() + 1))))
            window.alert('Rok ' + this.value + ' je divný!');"></td>

                            <!-- datum platby -->
                            <td align="right"><input type="text" name="datumplatby_<?php echo $i; ?>"
                                                     id="datumplatby_<?php echo $i; ?>" size="10"
                                                     value="<?php if (isset($auto_platba_datum_p)) echo $auto_platba_datum_p;
                                                     else echo date("d.m.Y"); ?>"


                                                     onchange="document.getElementById('datum_casopis_<?php echo $i; ?>').value = this.value;"
                                                     onblur="document.getElementById('datum_casopis_<?php echo $i; ?>').value = this.value;">
                            </td>
                            <!-- pri zmenen a ztrate focusu se hodnota prenese take do data casopis-->

                            <!-- platby clenstvi -->
                            <td align="right"><input type="text" name="hodnota1_<?php echo $i; ?>" size="10"
                                                     value="<?php if (isset($auto_platba_hodnota1)) echo $auto_platba_hodnota1; ?>">
                            </td>
                            <td><input type="text" name="hodnota2_<?php echo $i; ?>" size="10"
                                       value="<?php if (isset($auto_platba_hodnota2)) echo $auto_platba_hodnota2; ?>">
                            </td>


                            <td><input type="text" name="datum_casopis_<?php echo $i; ?>"
                                       id="datum_casopis_<?php echo $i; ?>" size="10"
                                       value="<?php if (isset($auto_platba_datum_c)) echo $auto_platba_datum_c; ?>">
                            </td>


                            <td><input type="text" name="hodnota3_<?php echo $i; ?>" id="hodnota3_<?php echo $i; ?>"
                                       size="10"
                                       value="<?php if (isset($auto_platba_hodnota3)) echo $auto_platba_hodnota3; ?>"
                                       onchange="document.getElementById('hodnota4_<?php echo $i; ?>').value = this.value;"
                                       onblur="document.getElementById('hodnota4_<?php echo $i; ?>').value = this.value;">
                            </td>
                            <td><input type="text" name="hodnota4_<?php echo $i; ?>" id="hodnota4_<?php echo $i; ?>"
                                       size="10"
                                       value="<?php if (isset($auto_platba_hodnota4)) echo $auto_platba_hodnota4; ?>">
                            </td>


                            <td><input type="text" name="pozn_<?php echo $i; ?>" size="60"
                                       value="<?php if (isset($auto_platba_poznamka) && $auto_platba_rozpoznana) echo $auto_platba_poznamka; ?>">
                            </td>
                        </tr>

                        <?php
                        // další záznamy
                        $SQL = "
                    SELECT
                        * ,
                        DATE_FORMAT(datum_prizpevky, '%e.%c.%Y') datumplatby1,
                        DATE_FORMAT(datum_casopis, '%e.%c.%Y') datum_casopis1
                    FROM
                        mensasec.c_m_platby WHERE c_id_m = {$c_id_m}
                    ORDER BY
                        rok DESC";

                        $rs = $db2->Query($SQL);
                        $old_zaplacenodo = 0;

                        if ($db2->getNumRows($rs) > 0) {
                            while ($platba = $db2->FetchArray($rs)) {   // vypis zaznamu z databaze
                                $i++;
                                ?>
                                <tr>
                                <td><input type="text" name="rok_<?php echo $i; ?>" size="4"
                                           value="<?php echo $platba["rok"]; ?>"></td>
                                <td><input type="text" name="datumplatby_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["datumplatby1"] ?>"></td>
                                <td><input type="text" name="hodnota1_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["hodnota1"]; ?>"></td>
                                <td><input type="text" name="hodnota2_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["hodnota2"]; ?>"></td>
                                <td><input type="text" name="datum_casopis_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["datum_casopis1"]; ?>"></td>
                                <td><input type="text" name="hodnota3_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["hodnota3"]; ?>"></td>
                                <td><input type="text" name="hodnota4_<?php echo $i; ?>" size="10"
                                           value="<?php echo $platba["hodnota4"]; ?>"></td>
                                <td><input type="text" name="pozn_<?php echo $i; ?>" size="60"
                                           value="<?php echo $platba["pozn"]; ?>"></td>
                                </tr><?php
                            }
                            // vyloupne rok z vrchni rady - vzhledem k razeni dotazu to je ten posledni
                            $old_zaplacenodo = $db2->getResult($rs, 0, "rok");
                        }
                        ?>
                    </table>


                    <!-- interni informace o placeni prispevku -->
                    <input type="hidden" name="old_zaplacenodo" value="<?php echo $old_zaplacenodo; ?>">


                    <?php
                    $letos = Date("Y");

                    // zobrazeni varovani, ze se jedna o neplatice
                    // pokud neukoncil
                    if ($row['ukonceni_clenstvi_poplatky'] == 0 AND $old_zaplacenodo < date("Y")) {
                        if ($row['ukonceni_clenstvi_poplatky'] < $old_zaplacenodo) {
                            if ($evidence_dm_neplaticu OR $row["typ"] != 2) {
                                if (!($row["typ"] == 2 && $roknar >= date('Y', strtotime('-16 years')))) { // detska mensa, vice nez 16 let
                                    ?>
                                    <div style="border:solid 2px #FF0000; width:300px; position:absolute; left:265px; top:172px; padding:3px; color:#FF0000; background-color:#99FF00;">
                                        Evidován jako NEPLATIČ!!!
                                    </div>
                                    <?php
                                }
                            }
                        }
                    }
                    ?>
                </td>
            </tr>
            <!-- konec prispevku -->

            <tr>
                <td><b>Zápisné</b></td>
                <td>
                    částka zápisného<br>
                    <input type="text" name="zapisne" size="10" value="<?php
                    // doplň částku zápisného, pokud je názma a není již zadána jiná hodnota
                    if (trim($row["zapisne"]) == '' AND isset($auto_platba_zapisne))
                        echo $auto_platba_zapisne;
                    else
                        echo $row["zapisne"];
                    ?>">
                </td>
                <td colspan="2">
                    datum zaplacení zápisného<br>
                    <input type="text" name="zapisne_placeno" size="10" value="<?php
                    // doplň datum zápisného, pokud je známo a není již jiná hodnota
                    if (trim($row["zapisne_placeno2"]) == '' AND isset($auto_platba_zapisne_datum))
                        echo $auto_platba_zapisne_datum;
                    echo $row["zapisne_placeno2"];
                    ?>">
                </td>
            </tr>

            <tr>
                <td><b>Přihláška</b>
                    <br>
                    <small>údaje z přihlášky</small>
                </td>
                <td>
                    <p>
                        vznik členství<br>
                        <input type="text" name="datum_vstupu" size="10"
                               value="<?php echo @$priznaky["datum_vstupu"]; ?>">
                        <input type="hidden" name="datum_vstupu_old" value="<?php echo @$priznaky["datum_vstupu"]; ?>">
                        <br>
                        <small>pozdější z dat platby nebo doručení přihlášky</small>

                        <!--
                        <br>
                        <small>datum podpisu nebo doručení přihlášky</small>-->
                    </p>
                </td>

                <!-- zaskrtavatka na vitani -->
                <td colspan="2">
                    <p>zaškrtněte, pokud si člen <b>ne</b>přeje být vítán<br>
                        <input type="checkbox" name="nepreje_vitani_casopisem"
                               id="nepreje_vitani_casopisem" <?php if (@$priznaky["nevitat_v_casopise"] == 1) echo " checked='checked'"; ?> >
                        <input type="hidden" name="nepreje_vitani_casopisem_old"
                               id="nepreje_vitani_casopisem_old"
                               value="<?php if (@$priznaky["nevitat_v_casopise"] == 1) echo "on"; ?>">
                        <label for="nepreje_vitani_casopisem">nepřeje si být přivítán v <b>časopise</b></label><br>

                        <input type="checkbox" name="nepreje_vitani_clenem"
                               id="nepreje_vitani_clenem" <?php if (@$priznaky["nevitat_clenem"] == 1) echo " checked='checked'"; ?>>
                        <input type="hidden" name="nepreje_vitani_clenem_old"
                               id="nepreje_vitani_clenem_old"
                               value="<?php if (@$priznaky["nevitat_clenem"] == 1) echo "on"; ?>">
                        <label for="nepreje_vitani_clenem">nepřeje si být přivítán <b>členem</b></label>
                    </p>
                </td>
            </tr>

            <tr>
                <td><b>Dětská Mensa</b>
                    <br>
                    <small>jen pro stará pravidla</small>
                </td>
                <td>
                    výše příspěvku DM<br>
                    <input type="text" name="prizpevek" size="10" value="<?php echo $row["prizpevek"]; ?>" <?php
                    if ($row["typ"] == 1) {
                        echo ' disabled="disabled" ';
                    }
                    ?> >
                </td>
                <td colspan="2">
                    datum zaplacení<br>
                    <input type="text" name="prizpevek_placeno" size="10"
                           value="<?php echo $row["prizpevek_placeno2"]; ?>" <?php
                    if ($row["typ"] == 1) {
                        echo ' disabled="disabled" ';
                    }
                    ?> >
                </td>
            </tr>

            <tr>
                <td>
                    <b>Převod z Dětské Mensy</b><br>
                    <small>Tento člen byl dříve členem DM.</small>
                </td>
                <td colspan="3">
                    <p> členské číslo v Dětské Mense<br>
                        <!-- toto policko je zakazane pro cleny DM -->
                        <input type="text" name="byvale_clencislo_v_detske"
                               value="<?php echo @$priznaky["clencislo_v_dm"]; ?>"
                               size="5" <?php if ($row["typ"] == 2) echo ' disabled="disabled" '; ?> ><br>
                        <input type="hidden" name="byvale_clencislo_v_detske_old"
                               value="<?php echo @$priznaky["clencislo_v_dm"]; ?>">
                        <small>původní členské číslo</small>
                    </p>
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <td><span style="color: red;">Zrušení členství</span> (datum)<br>
                    <small>úmrtí, žádost o vymazání z DB<br>zároveň vyplňte i následující pole</small>
                </td>
                <td>
                    <input type="text" name="ukonceni_clenstvi" size="10"
                           value="<?php echo $row["ukonceni_clenstvi2"]; ?>" <?php
                    if (strlen($row["ukonceni_clenstvi2"]) > 0) {
                        echo "readonly";
                    }
                    ?> >
                </td>

                <td colspan="2">
                    <input type="checkbox" name="nezasilat" id="nezasilat" value="1"<?php
                    if ($row["nezasilat_casopis"] == 1) {
                        echo " checked";
                    }
                    ?>>
                    <label for="nezasilat">nezasílat časopis</label><br>
                    <small>je členem, ale explicitně nechce zasílat tištěný časopis</small>
                </td>
            </tr>

            <tr>
                <td>Ukončení členství (rok)<br>
                    <small>poslední rok, kdy zaplatil poplatky</small>
                </td>
                <td colspan="3">
                    <input type="text" name="ukonceni_clenstvi_poplatky" size="10"
                           value="<?php if ($row["ukonceni_clenstvi_poplatky"] > 0) echo($row["ukonceni_clenstvi_poplatky"]); ?>">
                    <small>dlužník / řádný člen (vyplňujeme pokud požádá o pozastavení členství, při obnovení členství
                        mažeme)
                    </small>
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <td>narozen</td>
                <td colspan="3"><input type="text" name="datumnar" size="10"
                                       value="<?php echo $row["datumnar2"]; ?>"<?php echo $zamknute_pole; ?>>
                    (věk <?php echo $vek; ?>)
                </td>
            </tr>

            <tr>
                <td>rodné příjmení</td>
                <td colspan="3"><input type="text" name="rodne_prijmeni" value="<?php echo $row["rodne_prijmeni"]; ?>">
                </td>
            </tr>

            <!-- IQ -->
            <tr>
                <td colspan="4">
                    <small>
                        Dospělá Mensa: vyplňuje se IQ, percentil se nepoužívá.<br>
                        Dětská Mensa: do políčka IQ se zadává počet správných odpovědí, vyplňuje se percentil.
                    </small>
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <td colspan="4"><strong>Raven</strong></td>
            </tr>

            <tr>
                <td>Datum testu 1</td>
                <td colspan="4">
                    <?php $raven_iq1 = $aes_crypt->decrypt($row["iq1"]); ?>

                    <input type="text" name="datumtestu1" size="10"
                           value="<?php echo $raven_iq1 ? $row["datumtestu12"] : ""; ?>">&nbsp;&nbsp;&nbsp;
                    IQ 1 <input type="text" name="iq1" size="4" value="<?php echo $raven_iq1; ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    Percentil 1 <input type="text" name="per1" size="4"
                                       value="<?php echo $aes_crypt->decrypt($row["per1"]); ?>">
                </td>
            </tr>

            <tr>
                <td>Datum testu 2</td>
                <td colspan="4">
                    <input type="text" name="datumtestu2" size="10" value="<?php echo $row["datumtestu22"]; ?>">&nbsp;&nbsp;&nbsp;
                    IQ 2 <input type="text" name="iq2" size="4" value="<?php echo $aes_crypt->decrypt($row["iq2"]); ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    Percentil 2 <input type="text" name="per2" size="4"
                                       value="<?php echo $aes_crypt->decrypt($row["per2"]); ?>">
                </td>
            </tr>

            <tr>
                <td>Datum testu 3</td>
                <td colspan="4">
                    <input type="text" name="datumtestu3" size="10" value="<?php echo $row["datumtestu32"]; ?>">&nbsp;&nbsp;&nbsp;
                    IQ 3 <input type="text" name="iq3" size="4" value="<?php echo $aes_crypt->decrypt($row["iq3"]); ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    Percentil 3 <input type="text" name="per3" size="4"
                                       value="<?php echo $aes_crypt->decrypt($row["per3"]); ?>">
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <!-- =================================== MITCH ========================================================= -->
            <tr>
                <td class="mitch-highlight"><strong>Mitch</strong></td>
                <td class="mitch-highlight" colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <td class="mitch-highlight">Datum testu 1</td>
                <td class="mitch-highlight" colspan="4">
                    <?php
                        // Pozor, pole mitch_datum_1 je u nove otestovanych vzdy vyplneno, i kdyz test nedelal!
                        // Pokud neni hodnota pole mitch_odpovedi_1 znama, vrati desifrovaci funkce prazdny string.
                        $mitch_odpovedi_1 = $aes_crypt->decrypt($row["mitch_odpovedi_1"]);
                        ?>

                    <input type="text" name="mitch_datum_1" size="10"
                           value="<?php echo ($mitch_odpovedi_1 != "") ? $row["mitch_datum_1"] : ""; ?>">&nbsp;&nbsp;&nbsp;
                    Počet odpovědí 1 <input type="text" name="mitch_odpovedi_1" size="4"
                                            value="<?php echo $mitch_odpovedi_1; ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    IQ 1 <input type="text" name="mitch_iq_1" size="4"
                                value="<?php echo $aes_crypt->decrypt($row["mitch_iq_1"]); ?>">
                </td>
            </tr>

            <tr>
                <td class="mitch-highlight">Datum testu 2</td>
                <td class="mitch-highlight" colspan="4">
                    <input type="text" name="mitch_datum_2" size="10" value="<?php echo $row["mitch_datum_2"]; ?>">&nbsp;&nbsp;&nbsp;
                    Počet odpovědí 2 <input type="text" name="mitch_odpovedi_2" size="4"
                                            value="<?php echo $aes_crypt->decrypt($row["mitch_odpovedi_2"]); ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    IQ 2 <input type="text" name="mitch_iq_2" size="4"
                                value="<?php echo $aes_crypt->decrypt($row["mitch_iq_2"]); ?>">
                </td>
            </tr>

            <tr>
                <td class="mitch-highlight">Datum testu 3</td>
                <td class="mitch-highlight" colspan="4">
                    <input type="text" name="mitch_datum_3" size="10" value="<?php echo $row["mitch_datum_3"]; ?>">&nbsp;&nbsp;&nbsp;
                    Počet odpovědí 3 <input type="text" name="mitch_odpovedi_3" size="4"
                                            value="<?php echo $aes_crypt->decrypt($row["mitch_odpovedi_3"]); ?>">&nbsp;&nbsp;&nbsp;&nbsp;
                    IQ 3 <input type="text" name="mitch_iq_3" size="4"
                                value="<?php echo $aes_crypt->decrypt($row["mitch_iq_3"]); ?>">
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <td colspan="2"><strong>Korespondenční adresa</strong></td>
                <td colspan="2"><strong>Trvalé bydliště</strong></td>
            </tr>

            <tr>
                <td colspan="4">
                    <small>Prosím, jakýkoliv neplatný údaj (email, telefon atd.)
                        přesuňte do poznámky s dodatkem neplatný, aby se neobjevoval v automatických výpisech.
                    </small>
                </td>
            </tr>

            <tr>
                <td>byt</td>
                <td><input type="text" name="byt" size="30" value="<?php echo $row["byt"]; ?>"></td>
                <td>byt</td>
                <td><input type="text" name="byt2" size="30" value="<?php echo $row["byt2"]; ?>"></td>
            </tr>


            <tr>
                <td>ulice</td>
                <td><input type="text" name="ulice" size="30" value="<?php echo $row["ulice"]; ?>">
                    <?php
                    if (@$intra["ulice"] != null) {
                        echo "<br>{$intra['ulice']} (v intranetu)";
                    }
                    ?></td>
                <td>ulice</td>
                <td><input type="text" name="ulice2" size="30" value="<?php echo $row["ulice2"]; ?>"></td>
            </tr>


            <tr>
                <td>obec</td>
                <td><input type="text" name="obec" size="30" value="<?php echo $row["obec"]; ?>">
                    <?php
                    if (@$intra["mesto"] != null) {
                        echo "<br>{$intra['mesto']} (v intranetu)";
                    }
                    ?></td>
                <td>obec</td>
                <td><input type="text" name="obec2" size="30" value="<?php echo $row["obec2"]; ?>"></td>
            </tr>


            <tr>
                <td>PSČ
                    <small>s mezerou</small>
                </td>
                <td><input type="text" name="psc" size="7" value="<?php echo $row["psc"]; ?>">
                    <?php
                    if (@$intra["psc"] != null) {
                        echo "<br>{$intra['psc']} (v intranetu)";
                    }
                    ?></td>
                <td>PSČ</td>
                <td><input type="text" name="psc2" size="7" value="<?php echo $row["psc2"]; ?>"></td>
            </tr>


            <tr>
                <td colspan="4">
                    <small>Pokud je v poznámce <b>adresa neplatí</b> (a nic dalšího, pozor na případné mezery),
                        nebude se člen vůbec objevovat v exportu pro PostServis.<br>
                        Stejně tak pokud je v poznámce <b>zahraničí</b> (a nic dalšího), člen se neobjeví v exportu pro
                        časopis.
                        Pečlivé vedení těchto příznaků navíc usnadní diagnostiku a nápravu.
                    </small>
                </td>
            </tr>


            <tr>
                <td>poznámka k adrese</td>
                <td><input type="text" name="adr_pozn" size="30" value="<?php echo $row["adr_pozn"]; ?>"></td>
                <td>poznámka k adrese</td>
                <td><input type="text" size="30" name="adr_pozn2" value="<?php echo $row["adr_pozn2"]; ?>"></td>
            </tr>


            <tr>
                <td>tel. priv.
                    <small>formát: 000 000 000</small>
                </td>
                <td colspan="3"><input type="text" name="telefon" size="30"
                                       value="<?php $rozpal = explode("|", $row["telefon"]);
                                       echo $rozpal[0]; ?>">
                    <?php
                    if (@$intra["tel_d"] != null) {
                        echo "<br>{$intra['tel_d']} (v intranetu)";
                    }
                    ?></td>
            </tr>


            <tr>
                <td>mobil
                    <small>formát: 000 000 000</small>
                </td>
                <td colspan="3"><input type="text" name="mobil" size="30"
                                       value="<?php $rozpal = explode("|", $row["mobil"]);
                                       echo $rozpal[0]; ?>">
                    <?php
                    if (@$intra["mobil"] != null) {
                        echo "<br>{$intra['mobil']} (v intranetu)";
                    }
                    ?></td>
            </tr>

            <tr>
                <td>tel. zam.
                    <small>formát: 000 000 000</small>
                </td>
                <td colspan="3"><input type="text" name="telefon_zam" size="30"
                                       value="<?php echo $row["telefon_zam"]; ?>">
                    <?php
                    if (@$intra["tel_p"] != null) {
                        echo "<br>{$intra['tel_p']} (v intranetu)";
                    }
                    ?></td>
            </tr>


            <tr>
                <td>
                    e-mail<br>
                    <small>
                        Do centrální databáze <strong>ne</strong>pište<br>
                        mensovní email. Chceme, aby<br>
                        platil i po ukončení členství.
                    </small>
                </td>

                <td colspan="3">
                    <?php $overenidne = \DateTime::createFromFormat("Y-m-d H:i:s", $row["email_verify_time"]); ?>
                    <input type="hidden" name="email_status" value="<?php echo $row["email_status"]; ?>">
                    <input type="hidden" name="email_verify_time" value="<?php echo $row["email_verify_time"]; ?>">
                    <input type="hidden" name="email_verify_ip" value="<?php echo $row["email_verify_ip"]; ?>">
                    <input type="text" name="email" size="40" value="<?php echo $row["email"]; ?>"
                        <?php if ($row['email_status'] == 1){ ?>
                           title="Email byl ověřen dne <?php echo $overenidne->format("d. m. Y H:i:s"); ?> z IP <?php echo $row["email_verify_ip"]; ?>"
                           style="background-color:#0F0"> <span color="#0F0">Ověřený</span>
                <?php } else { ?>
                    style="background-color:#F00" > <span style="color:#F00">Neověřený</span>
                <?php } ?>
                    <br>
                    <?php
                    if ($row["id_m"] > 0) {
                        echo $intra["email"] . " (v intranetu) <br>";
                        // uloz intranetovy mail do formulare pro dalsi odeslani
                        echo "<input type='hidden' name='email_intranet' value='{$intra['email']}'>\n";
                    }
                    ?>
                    <small>Vždy pište právě a pouze <b>JEDNU</b> emailovou adresu, další uveďte dole do poznámky.
                    </small>
                </td>
            </tr>

            <?php if ($row['vek'] < MAX_VEK_DOSPELI_EMAIL) { ?>
                <tr>
                    <td>
                        Jméno ZZ<br/>
                    </td>
                    <td colspan="3">
                        <input type="text" name="jmeno_zz" value="<?php echo $row["jmeno_zz"]; ?>" size="30">
                    </td>
                </tr>

                <tr>
                    <td>
                        e-mail zák. zástupce<br/>
                    </td>

                    <td colspan="3">
                        <?php $overenidne2 = \DateTime::createFromFormat("Y-m-d H:i:s", $row["email2_verify_time"]); ?>
                        <input type="hidden" name="email2_status" value="<?php echo $row["email2_status"]; ?>">
                        <input type="hidden" name="email2_verify_time"
                               value="<?php echo $row["email2_verify_time"]; ?>">
                        <input type="hidden" name="email2_verify_ip" value="<?php echo $row["email2_verify_ip"]; ?>">
                        <input type="text" name="email2" size="40" value="<?php echo $row["email2"]; ?>"
                            <?php if ($row['email2_status'] == 1){ ?>
                               title="Email by ověřen dne <?php echo $overenidne2->format("d. m. Y H:i:s"); ?> z IP <?php echo $row["email2_verify_ip"]; ?>"
                               style="background-color:#0F0"> <span color="green">Ověřený</span>
                    <?php } else { ?>
                        style="background-color:#F00" > <span style="color:red">Neověřený</span>
                    <?php } ?>
                        <br>
                        <?php
                        if ($row["id_m"] > 0) {
                            echo $intra["email"] . " (v intranetu) <br>";
                        }
                        ?>
                        <small>Vždy pište právě a pouze <b>JEDNU</b> emailovou adresu, další uveďte dole do poznámky.
                        </small>
                    </td>
                </tr>
            <?php } ?>


            <tr>
                <td>obecná poznámka
                    <br>
                    <small>
                        Pokud do poznámky vepíšete<br>
                        heslo PROBLÉM (velkým),<br>
                        objeví se záznam v <a href="./index.php?men=men19.2.14.0" target='_blank'>diagnostice</a>.
                    </small>
                </td>
                <td colspan="3"><textarea name="poznamka" cols="60" rows="5"><?php echo $row["poznamka"]; ?></textarea>
                </td>
            </tr>
            <!--<tr><td colspan="4">&nbsp;</td></tr>-->

            <tr>
                <td colspan="4"><strong>Certifikáty</strong><br>
                    <small>
                        Záznam o novém certifikátu se uloží pouze pokud je uveden údaj v konce cena.
                        <?php //echo "new_certifikat >>$new_certifikat<<, auto_platba_datum_p >>$auto_platba_datum_p<<, auto_platba_poznamka >>$auto_platba_poznamka<<";  ?>
                    </small>
                    <br>

                    <table border="0" cellpadding="2" cellspacing="0">
                        <tr>
                            <td>Cena</td>
                            <td>Počet certifikátů</td>
                            <td>Datum platby</td>
                            <td>Datum odeslání</td>
                            <td>Poznámka</td>
                        </tr>
                        <tr>
                            <td><input type="text" name="cert_cena_0" size="5"
                                       value="<?php if (isset($new_certifikat)) {
                                           echo "$new_certifikat";
                                       } ?>"></td>
                            <td><input type="text" name="cert_pocet_0" size="10"
										value="<?php if (isset($pocet_certifikatu,$new_certifikat)) {
                                           echo "$pocet_certifikatu";
                                       } ?>"></td>
							<td><input type="text" name="cert_platba_0" size="10"
                                       value="<?php if (isset($auto_platba_datum_p, $new_certifikat)) {
                                           echo "$auto_platba_datum_p";
                                       } ?>"></td>
                            <td><input type="text" name="cert_odeslano_0" size="10" value=""></td>
                            <td><input type="text" name="cert_pozn_0" size="40"
                                       value="<?php if (isset($auto_platba_poznamka, $new_certifikat)) {
                                           echo "$auto_platba_poznamka";
                                       } ?>"></td>
                        </tr>


                        <?php
                        $certSql = "SELECT *,DATE_FORMAT(datum_platby, '%e.%c.%Y') datum_platby2,DATE_FORMAT(datum_odeslani, '%e.%c.%Y') datum_odeslani2, pocet_certifikatu
                 FROM mensasec.c_m_certifikaty  WHERE c_id_m = $c_id_m   ORDER BY id_param desc";
                        $rs = $db2->Query($certSql);
                        if ($db2->getNumRows($rs) > 0) {
                            while ($platba = $db2->FetchArray($rs)) {
                                ?>
                                <tr>
                                    <td><input type="text" name="cert_cena_<?php echo $platba["id_param"]; ?>" size="5"
                                               value="<?php echo $platba["cena"]; ?>"></td>
                                    <td><input type="text" name="cert_pocet_<?php echo $platba["id_param"]; ?>" size="10"
											   value="<?php echo $platba["pocet_certifikatu"]; ?>"></td>
                                    <td><input type="text" name="cert_platba_<?php echo $platba["id_param"]; ?>"
                                               size="10" value="<?php echo $platba["datum_platby2"]; ?>"></td>
                                    <td><input type="text" name="cert_odeslano_<?php echo $platba["id_param"]; ?>"
                                               size="10" value="<?php echo $platba["datum_odeslani2"]; ?>"></td>
                                    <td><input type="text" name="cert_pozn_<?php echo $platba["id_param"]; ?>" size="40"
                                               value="<?php echo $platba["poznamka"]; ?>"></td>
                                </tr>
                                <?php
                            }
                        }
                        ?>

                    </table>
                </td>
            </tr>

            <tr>
                <td colspan="4">&nbsp;</td>
            </tr>

            <tr>
                <!-- =============== Prukazy ===================== -->
                <!-- =============== Prukazy ===================== -->
                <!-- =============== Prukazy ===================== -->
                <td colspan="4"><strong>Průkazy</strong><br>
                    <table border="0" cellpadding="2" cellspacing="2">
                        <tr>
                            <th>Blokace</th>
                            <th>Typ</th>
                            <th>Do r.</th>
                            <th>Na jméno</th>
                            <th>Číslo</th>
                            <th>Zasláno na</th>
                            <th>Objednáno</th>
                            <th>Objednal</th>
                            <th>Web</th>
                        </tr>

                        <?php
                        $SQL = "
                SELECT
                    p.*,
                    -- musi byt prevedeno na true a false tady, jinak se to v PHP pokoni
                    if(p.blokovana = 0, FALSE, TRUE) blok,
                    DATE_FORMAT(p.datum_objednani, '%e.%c.%Y') objednano,
                    CONCAT(m.jmeno, ' ', m.prijmeni) objednatel
                FROM
                    mensasec.c_m_evidence_prukazu p LEFT OUTER JOIN
                    mensaweb.m_members m ON (p.objednal = m.id_m)
                WHERE
                    c_id_m = {$c_id_m}
                ORDER BY
                    blokovana ASC,
                    datum_objednani DESC";


                        $rs = $db2->Query($SQL);
                        // vypis vysledky
                        if ($db2->getNumRows($rs) > 0) {
                            while ($prukaz = $db2->FetchAssoc($rs)) {
                                // print_r($prukaz);
                                // blokovane prukazy jsou sede
                                echo "<tr style='" . ($prukaz['blok'] == TRUE ? "color: gray;" : "") . "'>";
                                if ($prukaz["blok"] == FALSE)
                                    echo "<td><input type='checkbox' name='blokovat_{$prukaz['qrid']}' id='blokovat_{$prukaz['qrid']}'>
                             <label for='blokovat_{$prukaz['qrid']}'>blokovat</label></td>";
                                else
                                    echo "<td><input type='checkbox' name='odblokovat_{$prukaz['qrid']}' id='odblokovat_{$prukaz['qrid']}'>
                             <label for='odblokovat_{$prukaz['qrid']}'>aktivovat</label></td>";
                                echo "<td>" . $prukaz["typ_prukazu"] . "</td>";
                                echo "<td>" . $prukaz["platnost_do_roku"] . "</td>";
                                echo "<td>" . $prukaz["jmeno_na_karte"] . "</td>";
                                echo "<td>" . $prukaz["clencislo_na_karte"] . "</td>";
                                echo "<td>" . $prukaz["zasilaci_adresa"] . "</td>";
                                echo "<td>" . $prukaz["objednano"] . "</td>";
                                echo "<td>" . $prukaz["objednatel"] . "</td>";
                                echo "<td><a target='_new' href='http://www.mensa.cz/qr?id=" . $prukaz["qrid"] . "'>" . $prukaz["qrid"] . "</a></td></tr>";
                            }
                        } else
                            echo "<td colspan='8'>Nemá žádný průkaz.</td>";
                        ?>
                    </table>


                    <ul>
                        <li>
                            Zaškrtnutím čtverečku blokovat dojde k explictinímu zablokování průkazu.
                            To je třeba udělat v příapdě ztráty nebo výměny průkazu.
                        </li>
                        <li>
                            V případě zániku členství (jakýmkoliv způsobem)
                            se bude průkaz automaticky chovat jako neplatný
                            (ač nebude zablokován explicitně).
                        </li>
                        <li>
                            V případě obnovení členství se automaticky zneplatněný
                            průkaz sám obnoví. Manuálně zneplatněný průkaz
                            nelze obnovit (nijak).
                        </li>
                        <li>
                            Jeden člen může mít více aktivních průkazů,
                            více průkazů však nelze stnadardním způsobem objednat.
                        </li>

                    </ul>
                </td>
            </tr>

            <!-- ulozeni -->
            <?php
            if (access("update", $men, $a_user["id_m"], $db2)) {
            ?>
            <tr>
                <td colspan="4">
                    <p>
                        <!-- disablovani buttonu ho vyjme z odeslilanych dat, je treba mit nahradni -->
                        <input type="hidden" name="odeslat" value="Odeslat">
                        <!-- onclick="this.disabled=true;" reseno jako onsubmit akce formulare, coz je obsirnejsi nez onclick knofliku-->
                        &nbsp;<br>
                        <input type="submit" name="knoflik" id="knoflik" value="Odeslat">
                    </p>
                </td>
            </tr>
        </table>
    </form>
<?php } else echo "</table>"; ?>

    <hr>
    <?php
    if ($c_id_m != -1) {
        include("popup_tisk_form.php");
        certifikaty ($men, $c_id_m, $n_id_m);
        etikety ($men, $c_id_m, $n_id_m);
    }
    ?>
    <div>&nbsp;</div>


    <h3 style="margin-top: 2em;">Poznámky</h3>
    <ul>
        <li>Červeně podbarvená políčka by se již neměla měnit, jsou uložena na více místech a nedošlo by ke korektní
            propagaci změn.
        </li>
        <li>Platbu, u které není zadán rok, systém přeskočí (pokud je smazán rok u existující platby, bude smazána
            celá).
        </li>
        <li>Při jakékoliv manipulaci s datem platby příspěvku se datum automaticky přepíše i do data platby časopisu.
        </li>
        <li>Formát data: DD.MM.RRRR</li>
        <li><strong>Provoz na této stránce je monitorován a zaznamenáván.</strong></li>
    </ul>

    <h3 style="margin-top: 2em;">Barevný klíč</h3>
    <table>
        <tr>
            <td style="background-color: #888888;">požádal o vymazání údajů</td>
        </tr>
        <tr>
            <td style="background-color: #aaaaaa;">požádal o ukončení členství</td>
        </tr>
        <tr>
            <td style="background-color: #ccaaaa;">člen DM, který je moc starý</td>
        </tr>
        <tr>
            <td style="background-color: #FFFFFF;">nový záznam (není v databázi)</td>
        </tr>
        <tr>
            <td style="background-color: #FFFFcc;">nemá členské číslo, nikdy nevstoupil</td>
        </tr>
        <tr>
            <td style="background-color: #ffddaa;">má členské číslo, ale nikdy neplatil a není DM dle starých podmínek
            </td>
        </tr>
        <tr>
            <td style="background-color: #f8adff;">platný člen, ale neplatil tento rok</td>
        </tr>
        <tr>
            <td style="background-color: #FFaa88;">propadlý člen – přestal platit, ale nevystoupil</td>
        </tr>
        <tr>
            <td style="background-color: #aaaaFF;">platný člen (dítě i dospělý)</td>
        </tr>
    </table>
</div>
