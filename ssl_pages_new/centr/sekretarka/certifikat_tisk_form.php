<?php

function papir($format)
{
    /* default rozmer je A4 ve tvaru sirka_vyska*/
    switch ($format) {
        case "A0":
            $rozmer_papiru = array(841, 1189);
            break;
        case "A1":
            $rozmer_papiru = array(594, 841);
            break;
        case "A2":
            $rozmer_papiru = array(420, 594);
            break;
        case "A3":
            $rozmer_papiru = array(297, 420);
            break;
        default:
            $rozmer_papiru = array(210, 297);
    }
    return $rozmer_papiru;
}

function etiketa($mpdf, $papir, $tisk, $hodnoty, $men)
{
    $zacatek_x = $hodnoty[0];
    $zacatek_y = $hodnoty[1];
    $sirka_etikety = $hodnoty[2];
    $vyska_etikety = $hodnoty[3];
    $mezera_x = $hodnoty[4];
    $mezera_y = $hodnoty[5];
    $rozmer_papiru = papir($papir);
    $mpdf->SetTitle("Etikety");
    $obsahPdf = '
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="cs" xml:lang="cs" xmlns="http://www.w3.org/1999/xhtml">
        <head>
            <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
            <style type="text/css" media="print, screen">
                /* definice papiru pro PDF */
                @page {
                    size:           ' . ($rozmer_papiru[0]) . 'mm ' . ($rozmer_papiru[1]) . 'mm;
                    margin-top:     0cm;
                    margin-right:   0cm;
                    margin-bottom:  0cm;
                    margin-left:    0cm;
                }
                div.adresa
                {
                    border: none;
                    width: ' . $sirka_etikety . 'mm;
                    height: ' . $vyska_etikety . 'mm;
                    position: absolute;
                    text-align: left;
                    overflow: hidden;
                }
                div.adresa2
                {
                    border: none;
                    height: 100%;
                    width: 100%;
                    margin: 0;
                    overflow: hidden;
                    box-sizing: border-box; 
                }
                div.adresa3
                {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                }
            </style>
        </head>
        <body>
    ';
    $pocet_x = floor($rozmer_papiru[0] / ($sirka_etikety + $mezera_x));
    $pocet_y = floor($rozmer_papiru[1] / ($vyska_etikety + $mezera_y));
    if ((($pocet_x + 1) * ($sirka_etikety + $mezera_x) - $mezera_x) <= $rozmer_papiru[0]) $pocet_x++;
    if ((($pocet_y + 1) * ($vyska_etikety + $mezera_y) - $mezera_y) <= $rozmer_papiru[1]) $pocet_y++;
    $left_rozmer = ($rozmer_papiru[0] - ($pocet_x * ($sirka_etikety + $mezera_x)) + $mezera_x) / 2;
    $top_rozmer = ($rozmer_papiru[1] - ($pocet_y * ($vyska_etikety + $mezera_y)) + $mezera_y) / 2;
    $left_pocatek = $left_rozmer + ($sirka_etikety + $mezera_x) * ($zacatek_x - 1);
    $top_pocatek = $top_rozmer + ($vyska_etikety + $mezera_y) * ($zacatek_y - 1);
    $left = $left_pocatek;
    $top = $top_pocatek;
    $mpdf->WriteHTML($obsahPdf);
    foreach ($tisk as $row) {
        if ($left > ($rozmer_papiru[0] - $sirka_etikety)) {
            $left = $left_rozmer;
            $top += $vyska_etikety + $mezera_y;
            if ($top > ($rozmer_papiru[1] - $vyska_etikety)) {
                $top = $top_rozmer;
                $mpdf->AddPage();
            }

        }
        $obsahPdf = ("<div class=\"adresa\" style=\"left: {$left}mm; top: {$top}mm;\"><div class=\"adresa2\" style=\"");
        if (($top == $top_rozmer) && ($top_rozmer < 10)) {
            $obsahPdf .= ("padding-top: " . (10 - $top_rozmer) . "mm;");
        } else {
            $obsahPdf .= ("padding-top: 5mm;");
        }
        if ((($top + (2 * $vyska_etikety)) > $rozmer_papiru[1]) && ($top_rozmer < 10)) {
            $obsahPdf .= (" padding-bottom: " . (10 - $top_rozmer) . "mm;");
        } else {
            $obsahPdf .= (" padding-bottom: 5mm;");
        }
        if ((($left + (2 * $sirka_etikety)) > $rozmer_papiru[0]) && ($left_rozmer < 10)) {
            $obsahPdf .= (" padding-right: " . (10 - $left_rozmer) . "mm;");
        } else {
            $obsahPdf .= (" padding-right: 5mm;");
        }
        if (($left == $left_rozmer) && ($left_rozmer < 10)) {
            $obsahPdf .= (" padding-left: " . (10 - $left_rozmer) . "mm;");
        } else {
            $obsahPdf .= (" padding-left: 5mm;");
        }
        $obsahPdf .= ("\">");
        switch ($men) {
            case "men19.2.2.0":
            case "men19.2.18.0":
                $obsahPdf .= ("<div class=\"adresa3\">" . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row["titul_za_jmenem"]) ? (", " . $row["titul_za_jmenem"]) : "") . "<br>");
                if (!empty($row["byt"])) {
                    $obsahPdf .= ($row["byt"] . "<br>");
                }
                $obsahPdf .= ($row["ulice"] . "<br><b>");
                $obsahPdf .= ($row["PSC"] . " " . $row["mesto"] . "</b><br></div></div></div>");
                break;
            case "men19.1.0.0":
                $obsahPdf .= ("<table class=\"adresa3\">");
                $obsahPdf .= ("<tr><td colspan=\"3\" style=\"text-align: left;\">" . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row['titul_za_jmenem']) ? (", " . $row['titul_za_jmenem']) : "") . "<br>");
                $obsahPdf .= ("<tr><td style=\"width: 33%; text-align: left;\">" . $row["datum_testu"] . "</td><td style=\"width: 33%; text-align: center;\">" . $row["datum_narozeni"] . "</td><td style=\"width: 33%; text-align: right;\">" . $row["vek"] . "</td><tr>");
                $obsahPdf .= ("<tr><td  colspan=\"3\" style=\"text-align: left;\">" . $row["ulice"] . ", " . $row["mesto"] . ", " . $row["PSC"] . "</td></tr></table></div></div>");
                break;
        }
        $left += ($sirka_etikety + $mezera_x);
        $mpdf->WriteHTML($obsahPdf);
    }
    $obsahPdf = '</body></html>';
    $mpdf->WriteHTML($obsahPdf);
    return $mpdf;
}

/**
 * Stary certifikat
 * @param $mpdf
 * @param $tisk
 * @return mixed
 */
function certifikat($mpdf, $tisk)
{
    $rozmer_papiru = array(210, 297);
    $mpdf->SetTitle("Certifikaty");
    $obsahPdf = '
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="cs" xml:lang="cs" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
        <style type="text/css" media="print, screen">
            /* definice papiru pro PDF */
            @page {
                size:           ' . ($rozmer_papiru[0]) . 'mm ' . ($rozmer_papiru[1]) . 'mm;
                margin-top:     0cm;
                margin-right:   0cm;
                margin-bottom:  0cm;
                margin-left:    0cm;
            }
            /* reset vsech defaultnich margins */
            div.text
            {
                position: absolute;
                left: 50%;
                bottom: 0mm;
                margin-top: auto;
                margin-left: 0;
                margin-right:0;
                margin-bottom: 0;
                font-style: italic;
                font-family: "Comic Sans MS", "Comic Sans", "comic", cursive;
                text-align: center;
            }
            div.text2
            {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, 0%);
                font-style: italic;
                font-family: "Comic Sans MS", "Comic Sans", "comic", cursive;
                text-align: center;
            }
            div.hlavni
            {
                position: relative;
                top: 0mm;
                left: 0mm;
                width: 176mm;
                height: 250mm;
            }
            div.jmeno
            {
                font-size: 20pt;
                font-weight: bold;
                position: absolute;
                left: 35mm;
                top: 95mm;
                width: 106mm;
                height: 12mm;
            }
            div.datum_narozeni
            {
                font-size: 14pt;
                position: absolute;
                top: 105mm;
                left: 74mm;
                width: 30mm;
                height: 7mm;
            }
            div.datum_testu
            {
                font-size: 14pt;
                position: absolute;
                top: 184mm;
                left: 59mm;
                width: 30mm;
                height: 7mm;
            }
            div.datum_tisku
            {
                font-size: 14pt;
                position: absolute;
                top: 196mm;
                left: 59mm;
                width: 30mm;
                height: 7mm;
            }
            div.IQ1
            {
                font-size: 14pt;
                position: absolute;
                top: 159mm;
                left: 99mm;
                width: 20mm;
                height: 7mm;
            }
            div.IQ2
            {
                font-size: 14pt;
                position: absolute;
                top: 171mm;
                left: 99mm;
                width: 20mm;
                height: 7mm;
            }
            div.IQ3
            {
                font-size: 14pt;
                position: absolute;
                top: 184mm;
                left: 99mm;
                width: 20mm;
                height: 7mm;
            }
            div.percentil
            {
                font-size: 14pt;
                position: absolute;
                top: 196mm;
                left: 99mm;
                width: 20mm;
                height: 7mm;
            }
            </style>
    </head>
<body>
';
    $j = 1;

    foreach ($tisk as $row) {
        if ($row['pocet_certifikatu'] == NULL) {
            $opakovani = 1;
        } else {
            $opakovani = $row['pocet_certifikatu'];
        }
        for ($i = 1; $opakovani >= $i; $i++) {
            $mpdf->WriteHTML($obsahPdf);
            $mpdf->WriteHTML("<div class='hlavni'>");
            $mpdf->WriteHTML("<div class='jmeno'><div class='text2'>" . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row['titul_za_jmenem']) ? (", " . $row['titul_za_jmenem']) : "") . "</div></div>");
            $mpdf->WriteHTML("<div class='datum_narozeni'><div class='text'>" . $row["datum_narozeni"] . "</div></div>");
            $mpdf->WriteHTML("<div class='datum_tisku'><div class='text'>" . $row["datum_tisku"] . "</div></div>");
            $mpdf->WriteHTML("<div class='datum_testu'><div class='text'>" . $row["datum_testu"] . "</div></div>");
            $mpdf->WriteHTML("<div class='IQ1'><div class='text'>" . $row["IQ1"] . "</div></div>");
            $mpdf->WriteHTML("<div class='IQ2'><div class='text'>" . $row["IQ2"] . "</div></div>");
            $mpdf->WriteHTML("<div class='IQ3'><div class='text'>" . $row["IQ3"] . "</div></div>");
            $mpdf->WriteHTML("<div class='percentil'><div class='text'>" . $row["percentil"] . "</div></div>");
            $mpdf->WriteHTML("</div>");
            $mpdf->WriteHTML('</body></html>');
            if ($j != array_sum(array_column($tisk, 'pocet_certifikatu')) && ($row['pocet_certifikatu'] != NULL)) {
                $mpdf->AddPage();
            }
            $j++;
        }

    }

    return $mpdf;
}

/**
 * Novy certifikat
 * @param $mpdf
 * @param $tisk
 * @return mixed
 */
function certifikat_new($mpdf, $tisk)
{
    $rozmer_papiru = array(176, 250);
    $mpdf->SetTitle("Certifikát");
    $obsahPdf = '
<!DOCTYPE html>
<html lang="cs" xml:lang="cs" xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>Certifikát</title>
        <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
        <style media="print, screen">
            /* definice papiru pro PDF */
            @page {
                size:           ' . ($rozmer_papiru[0]) . 'mm ' . ($rozmer_papiru[1]) . 'mm;
                margin-top:     0;
                margin-right:   0;
                margin-bottom:  0;
                margin-left:    0;
            }
            /* reset vsech defaultnich margins */
            div.text
            {
                position: absolute;
                left: 50%;
                margin-top: auto;
                margin-left: 0;
                margin-right:0;
                margin-bottom: 0;
                font-family: "HelveticaNeueLTProMd";
            }
            div.text2
            {
                position: absolute;
                left: 50%;
                margin-top: auto;
                margin-left: 0;
                margin-right: 0;
                margin-bottom: 0;
                font-family: "HelveticaNeueLTProRoman";
            }
            div.hlavni
            {
                position: relative;
                top: 0;
                left: 0;
                width: 180mm;
                height: 250mm;
            }
            div.jmeno
            {
                font-size: 50pt;
                position: absolute;
                left: 10mm;
                top: 67mm;
                width: 156mm;
                height: 20mm;
                text-align: center;
            }
            div.datum_narozeni
            {
                font-size: 21pt;
                position: absolute;
                top: 97mm;
                left: 68mm;
                width: 40mm;
                height: 8mm;
                text-align: center;
            }
            div.datum_testu
            {
                font-size: 12pt;
                position: absolute;
                top: 191mm;
                left: 11mm;
                width: 30mm;
                height: 6mm;
                text-align: left;
            }
            div.datum_tisku
            {
                font-size: 12pt;
                position: absolute;
                top: 191mm;
                left: 134mm;
                width: 30mm;
                height: 6mm;
                text-align: right;
            }
            div.IQ1    
            {
                font-size: 21pt;
                position: absolute;
                top: 144mm;
                left: 63mm;
                width: 20mm;
                height: 8mm;
                text-align: left;
            }
            div.IQ2
            {
                font-size: 21pt;
                position: absolute;
                top: 155mm;
                left: 63mm;
                width: 20mm;
                height: 8mm;
                text-align: left;
            }
            div.IQ3
            {
                font-size: 21pt;
                position: absolute;
                top: 144mm;
                left: 92mm;
                width: 20mm;
                height: 8mm;
                text-align: right;
            }
            div.percentil
            {
                font-size: 21pt;
                position: absolute;
                top: 155mm;
                left: 82mm;
                width: 30mm;
                height: 8mm;
                text-align: right;
            }
            </style>
    </head>
<body>
';
    $j = 1;

    foreach ($tisk as $row) {
        if ($row['pocet_certifikatu'] == NULL) {
            $opakovani = 1;
        } else {
            $opakovani = $row['pocet_certifikatu'];
        }
        for ($i = 1; $opakovani >= $i; $i++) {
            $mpdf->WriteHTML($obsahPdf);
            $mpdf->WriteHTML("<div class='hlavni'>\n");
            $mpdf->WriteHTML("    <div class='jmeno'>     <div class='text'>" . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row['titul_za_jmenem']) ? (", " . $row['titul_za_jmenem']) : "") . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_narozeni'><div class='text2'>" . $row["datum_narozeni"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_tisku'><div class='text'>" . $row["datum_tisku"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_testu'><div class='text'>" . $row["datum_testu"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ1'>       <div class='text2'>" . htmlspecialchars($row["IQ1"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ2'>       <div class='text2'>" . htmlspecialchars($row["IQ2"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ3'>       <div class='text2'>" . htmlspecialchars($row["IQ3"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='percentil'> <div class='text2'>" . htmlspecialchars($row["percentil"]) . "</div></div>\n");
            $mpdf->WriteHTML("</div>\n");
            $mpdf->WriteHTML('</body></html>');
            if ($j != array_sum(array_column($tisk, 'pocet_certifikatu')) && ($row['pocet_certifikatu'] != NULL)) {
                $mpdf->AddPage();
            }
            $j++;
        }
    }
    return $mpdf;
}
