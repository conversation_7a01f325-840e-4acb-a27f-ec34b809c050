<?php
//  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
//  POZOR: Skript musí používat Unixové nové řádky, jinak se poškodí hlavičky mailů
//  a lidé je uvidí zprasené! (repektive pak MS Exchange poškodí hlavičky lidem,
//  kterým maily chodí přes ní a ti je pak uvidí zprasené, pokud to jejich klient
//  automaticky neopraví.

/**
 * detail-dopisy.i
 * Dopisy automaticky rozesílané členům
 * při operaci s jejich záznamem v databázi.
 *
 * Změnovník
 * 2013-Jun-08, TK: Komentáře, snaha o nápravu no-reply:  "-r <EMAIL>"
 * 2013-May-05, TK: Úprava kodovani promennych v dopisu pro nové členy.
 * 2013-Jan-03, TK: Kontrola kódování a úprava hlavi<PERSON> mailů, vyle<PERSON><PERSON><PERSON><PERSON> p<PERSON>drav<PERSON>.
 * 2012-May-09, TK: Odebrána zmínka o prefixu, emaily se nyní posílají bez cz.
 * 2012-Mar-20, TK: Kopie emailů chodí na logovací účet, nikoliv mně.
 * 2012-Mar-05, TK: Opravena chyba vynechávající tělo emailu a několik překlepů v emailu.
 * 2012-Feb-29, TK: Založeno, převeden kód z detail.i, doplněna možnost diakritiky v předmětu
 * 2014-Jan-06, PM: V txtu nahrazen znak "»", který se konertoval do t s háčkem
 * 2025-Mar-12, MD: Sendgrid Mailer
 */


////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
/**
 *    Zakódování e-mailové hlavičky podle RFC 2047
 * @param string text k zakódování
 * @param string kódování, výchozí je utf-8
 * @return string řetězec pro použití v e-mailové hlavičce
 * @copyright Jakub Vrána, http://php.vrana.cz/
 */
function mime_header_encode($text, $encoding = "utf-8")
{
    return "=?$encoding?Q?" . quoted_printable_encode($text) . "?=";
}


/**
 * Zasle email o tom, ze byla do databaze zadana nova platba.
 *
 * @global type $hlasky_pro_uzivatele sem se loguje činnost
 *
 * @param string $email cílový mail
 * @param string $rok
 * @return boolean
 */
function posli_email_o_zadani_platby($email, $rok, $prefix, $clenske_cislo, $jmeno)
{
    global $hlasky_pro_uzivatele;
    // debug $cilovy_email .= ", <EMAIL>";

    $predmet = "Potvrzení platby příspěvku na rok {$rok}";

    $header = "From: " . mime_header_encode("Mensa Česko", "utf-8") . " <<EMAIL>>
Bcc: <EMAIL>	
Return-Path: <EMAIL>
Content-Type: text/plain;charset=\"utf-8\"
Content-Transfer-Encoding: 8bit
MIME-Version: 1.0
X-Mailer: PHP/Mensaweb
";
// To: $cilovy_email

    $body = "Jméno: {$jmeno}
Členské číslo: {$prefix}{$clenske_cislo}

Dobrý den,

děkujeme Vám za zaplacení členského příspěvku na rok {$rok}. Tímto
potvrzujeme, že jsme jej obdrželi a můžete využívat všech výhod,
které členství v Mense přináší.

Podívejte se na novinky a kalendář akcí na intranetu Mensy
https://intranet.mensa.cz/ a na stránkách časopisu
http://casopis.mensa.cz/.

V případě jakýchkoliv dotazů se, prosím, obraťte na paní sekretářku
<NAME_EMAIL>. Uveďte Vaše členské číslo {$prefix}{$clenske_cislo}.

Budeme se na Vás těšit na některé z mnoha zajímavých akcí Mensy
nebo na intranetu Mensy,
s pozdravem a přáním krásného dne
Vaše Mensa Česko

--

Tato zpráva byla vygenerována automaticky.
";

    // odesle mail a vrati hodnotu funkce, ktera mail odesila
    // echo "<p> $header <br> $body_mail</p>";
    // bool mail ( string $to , string $subject , string $message [, string $additional_headers [, string $additional_parameters ]] )
    // Returns TRUE if the mail was successfully accepted for delivery, FALSE otherwise.
    if (isset($hlasky_pro_uzivatele)) array_push($hlasky_pro_uzivatele, "Posílám email <u>$predmet</u> na adresu <u>$email</u>.");
    $mailer = new mailer\Mailer();
    return $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $email, mime_header_encode($predmet, "utf-8"), $body, $header, "-r <EMAIL>");
}


/**
 * Tento email přijde většině nových členů.
 *
 * @global type $hlasky_pro_uzivatele sem se loguje činnost
 *
 * @param string $email cílový mail
 * @param string $prefix prefix členského čísla (dm nebo nic)
 * @param int $clencislo členské číslo
 * @param string $m_heslo uživatelské heslo do intranetu
 * @return boolean
 */
function posli_email_novemu_clenovi($email, $prefix, $clencislo, $m_heslo, $jmeno)
{
    global $hlasky_pro_uzivatele;
    $email = trim($email);

    $predmet = "Vítejte v dobré společnosti!";

    $header = "From: " . mime_header_encode("Mensa Česko", "utf-8") . " <<EMAIL>>
Bcc: <EMAIL>
Return-Path: <<EMAIL>>
Content-Type: text/plain;charset=\"utf-8\"
Content-Transfer-Encoding: 8bit
MIME-Version: 1.0
X-Mailer: PHP/Mensaweb
";
// To: $email

    $body = "Nový člen: {$jmeno}

Vítáme Vás v Mense!

Právě jsme Vám zřídili přístup do Intranetu Mensy Česko! Díky tomu můžete 
začít komunikovat s dalšími členy, zapojit se do mensovní diskuze, hledat
nové kamarády prostřednictvím vyhledávání členů z okolí, prohlížet
si fotografie z akcí, zalistovat kalendářem připravovaných akcí
nebo si během dlouhé chvíle projít starší čísla časopisu Mensa.

Do intranetu se můžete přihlásit na adrese https://intranet.mensa.cz/.
Přístupové jméno do intranetu je rovno Vašemu členskému číslu,
tj. {$prefix}{$clencislo} a vaše heslo je: {$m_heslo}, v nastavení
uživatelského profilu si jej můžete změnit.

K tomu, abyste mohli intranet využít skutečně plnohodnotně, Vás prosíme, 
abyste si doplnili Váš uživatelský profil a ověřili správnost v něm již 
uvedených údajů. Nastavení najdete na stránce Uživatelský profil - Nastavení.

Na stránce Uživatelský profil - Email @mensa.cz si můžete jedním
kliknutím zřídit emailovou schránku nebo alias (přesměrování) ve prestižním
tvaru <EMAIL>.

Na stránce Rozeslat zprávu - Příjem konferencí se můžete přihlásit k odběru
pozvánek na lokální akce a schůzky zájmových nebo místních skupin,
kterých jsou po celé republice desítky měsíčně (zatím Vám chodí pouze
informace o významných akcích, kterých je mnohem méně).

Na sesterské stránce Rozeslat zprávu - Příjem zpráv si můžete vybrat,
zda si přejete či nepřejete dostávat významné zprávy o probíhajících akcích
a vybrat si jaká témata a jaké regiony Vás zajímají; nastavili jsme Vám
příjem důležitých mensovních zpráv ze všech regionů, snadno to však můžete
změnit.

Pokud vás zajímá tematika nadaných dětí, prosíme, určitě se zaregistrujte
také na našem webu deti.mensa.cz (je třeba separátní registrace).

Členský průkaz očekávejte během
měsíce (na všech akcích jste samozřejmě vítání i bez průkazu).


Přejeme Vám příjemné chvíle s intranetem Mensy Česko
a rádi Vás uvidíme na některé z mnoha akcí Mensy,
s pozdravem a přáním krásného dne
Vaše Mensa Česko
";

    array_push($hlasky_pro_uzivatele, "Posílám email <u>$predmet</u> na adresu <u>$email</u>.");
    $mailer = new mailer\Mailer();
    return $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $email, mime_header_encode($predmet, "utf-8"), $body, $header, "-r <EMAIL>");
}


/**
 * Toto se jiz temer nepouziva
 *
 * @global type $hlasky_pro_uzivatele
 * @param type $email
 * @param type $prefix
 * @param type $clencislo
 * @param type $m_heslo
 * @return type
 */
function posli_email_novy_ucet($email, $prefix, $clencislo, $m_heslo)
{
    global $hlasky_pro_uzivatele;
    $email = trim($email);

    $predmet = "Přístup k intranetu Mensy Česko";

    $header = "From: " . mime_header_encode("Mensa Česko", "utf-8") . " <<EMAIL>>
Bcc: <EMAIL>
Return-Path: <EMAIL>
Content-Type: text/plain;charset=\"utf-8\"
Content-Transfer-Encoding: 8bit
MIME-Version: 1.0
X-Mailer: PHP/Mensaweb
";
//To: $email

    $body = "Dobrý den, 

právě jsme Vám zřídili přístup do Intranetu Mensy Česko! Díky tomu můžete 
začít komunikovat s dalšími členy, zapojit se do mensovní diskuze, najít 
si nové kamarády prostřednictvím vyhledávání členů z okolí, prohlédnout 
si veselé fotografie z proběhlých akcí, zalistovat kalendářem 
připravovaných akcí nebo si během dlouhé chvíle projít starší čísla 
časopisu Mensa a oficiální dokumenty jako je rozpočet, zápisy z jednání 
Rady a podobně.

K tomu, abyste mohli intranet využít skutečně plnohodnotně, Vás prosíme,
abyste si doplnili Váš uživatelský profil a ověřili správnost v něm již
uvedených údajů. Nastavení najdete na stránce Uživatelský profil - Nastavení.

Na stránce Uživatelský profil - Email @mensa.cz si můžete také jedním
kliknutím zřídit emailovou schránku nebo alias (přesměrování) ve tvaru
<EMAIL>.

Na stránce Rozeslat zprávu - Příjem zpráv si můžete vybrat, zda si přejete
či nepřejete dostávat zprávy o probíhajících akcích pro členy a vybrat si
jaká témata a jaké regiony Vás zajímají; nastavili jsme Vám příjem důležitých
mensovních zpráv ze všech regionů, snadno to však můžete změnit. Na sesterské
stránce Rozeslat zprávu - Příjem konferencí se můžete přihlásit k odběru
podrobnějších informací z konferencí zájmových nebo místních skupin.

Přístupové jméno do intranetu je rovno Vašemu členskému číslu,
tj. $prefix" . $clencislo . " a  vaše heslo je: $m_heslo, v nastavení 
uživatelského profilu si jej můžete změnit.

Do intranetu se můžete přihlásit na adrese: https://intranet.mensa.cz

Přejeme Vám příjemné chvíle s intranetem Mensy Česko
a rádi Vás uvidíme na nějaké z mnoha akcí Mensy,
s pozdravem a přáním krásného dne
Vaše Mensa Česko
";

    array_push($hlasky_pro_uzivatele, "Posílám email <u>$predmet</u> na adrersu <u>$email</u>.");
    $mailer = new mailer\Mailer();
    return $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $email, mime_header_encode($predmet, "utf-8"), $body, $header, "-r <EMAIL>");
}
