<?php

/**
 *
 *  @package mensa.cz
 *  <AUTHOR> <PERSON><PERSON><PERSON><PERSON>, <EMAIL>
 *  @version 1.0
 *
 *  Třída zpracování testování
 *  Využívají v ssl_pages_new/centr/testujici scripty main.i, new_test.i,
 *  Využívají v ssl_pages_new/centr/sekretarka/detail.i a detail-zapis.i
 *
 *  VAROVÁNÍ ministerstva kouzel!!!
 *  <PERSON><PERSON><PERSON>, kde je v<PERSON>b<PERSON><PERSON>, je potřeba vybírat jak podle testera, tak podle organizátora
 *
 *
 *  Používá tabulku
 * mensasec.c_m_otestovan
 * mensa_web.www_form_1
 * mensaweb.mc_akce
 *
 * Zmenovnik
 *  2022-06-14 HK: Další optimalizace získání registrovaných na testy
 *  2022-05-24 HK: Optimalizace query na získání registrovaných na testy
 *  2021-09-07 HK: <PERSON><PERSON>id<PERSON><PERSON> pole trida pro děti
 *  2019-01-08 AN: Řazení účastníků testování dle věku
 *  2014-10-31 TK: Rozšíření kontroly hodnot.
 *  2014-09-30 VK: přidání funkcí ReadUserData, ReadPrihlasku a protected standardPercentil pro tisk výsledků testů v detail.i
 *  2014-09-04 TK: Opravy rozmezí
 *  2014-06-18 VK: Zalozeni zmenovniku, úprava kontroly výsledků testů do dětské mensy
 */
define("MAX_VEK_DOSPELI_EMAIL",15);// od toho věku se neposílá dopis rodičům
define("MAX_VEK_DETSKY_TEST", 14); // v tomto veku lze naposledy delat test pro velke deti
define("MAX_VEK_MALE_DITE", 7); // v tomto veku lze naposledy delat test pro male deti

define("MIN_DATE_OF_TEST", "1991-03-28");
define("MIN_DATE_OF_BIRTH", "1900-01-01");

require_once("../ssl_pages_new/centr/sekretarka/platby-cenik.class.php");

use \crypto\CryptoInterface;
use \helpers\Dates;
use \mailer\MailerFactory;
use \mailer\MailerInterface;

class zpracovani_testovani {

    // PUBLIC
    public $inglob = null;                    // objekt  pro převod dat z GET a POST
    // PRIVATE
    private $dbsource = null;             // objekt databáze v mysql
    private $databasename = null;
    private $user = null;                 // aktuální uživatel
    private $del_user = null;             // smazaný uživatel - integer
    private $men = null;                    // odkaz na kód stránky
    private $id_tester = null;              // id testera
    private $dotazsql = null;                 // SQL dotaz
    private $vysledek = null;                 // pole s výsledky dotazu
    private $tmp = null;                      // výsledek sql dotazu v proceduře
    private $id_z = null;                       // id přihlášky do testování
    private $id = null;                           // id záznamu v otestovaných
    private $typ = null;                      // typ testu 1 dospělá mensa, 2 dětská mensa
    private $prihlaska = null;                // pole s přihláškou
    private $id_vyrazovaneho = null;              // identifikátor vyřazovaného testovaného bez zápisu do členů

    public function __construct($db, $database, $user, $menu) {
        /*
         * Nasetují se zdroje dat a uživatel
         */
        $this->dbsource = $db;
        $this->databasename = $database;
        $this->user = $user;
        $this->men = $menu;
        // knihovna pro kontrolu vstupu z $_GET a $_POST
        require_once('../ssl_library_2013/input_globals_class.php');
        $this->inglob = new input_globals();
    }

    /*
     * Pouze ukončí připojebí k databázi, pokud existuje
     */

    public function __destruct() {
        if (!is_null($this->dbsource)) {
            $this->dbsource->close();
        }
    }

    /*
     * Funkce provede ulozeni zmen
     * nastaveni zaznamu na neotestovan
     * Nevrací nic
     */

    public function SetNeotestovan($deluser) {

        global $PUBLIC_DATABASE_NAME, $SECURE_DATABASE_NAME;

        if (isset($deluser) && !is_null($deluser)) {
            $this->del_user = (int) $deluser;
            if (is_int($this->del_user) && $this->del_user > 0) {
                if (access("class", $this->men, $this->user["id_m"], $this->dbsource)) {
                    $SQL = "UPDATE {$PUBLIC_DATABASE_NAME}.www_form_1 SET stav= -1 WHERE  id_z={$this->del_user}";
                    if ($this->dbsource->Query($SQL)) {
                        echo "<br><font color='red'>Záznam byl nastaven do stavu <em>neotestován</em>, tuto akci nelze vrátit.</font><br>";
                    }
                }
                // bezny testujici - muze mazat jen sve zaznamy
                elseif (access("update", $this->men, $this->user["id_m"], $this->dbsource)) {
                    //kontrola, jestli je on testujicim
                    $tmp = $this->dbsource->Query("
                    SELECT mc_akce.id_owner, mc_akce.id_editor
                        FROM {$PUBLIC_DATABASE_NAME}.www_form_1 INNER JOIN {$PUBLIC_DATABASE_NAME}.mc_akce ON www_form_1.id_a = mc_akce.id_a
                        WHERE www_form_1.id_z={$this->del_user}");

                    if ($this->user["id_m"] == $this->dbsource->getResult($tmp, 0, "id_owner") || $this->user["id_m"] == $this->dbsource->getResult($tmp, 0, "id_editor")) {
                        $SQL = "UPDATE {$this->databasename}.www_form_1 SET stav= -1  WHERE   id_z={$this->del_user}";
                        $this->dbsource->Query($SQL);
                    }
                    else {
                        echo "Nemáte oprávnění pro úpravu stavů jiných testujících.";
                    }
                }
                // spatny uzivatel !!!
                else {
                    echo "Nemáte oprávnění pro editaci záznamů";
                }
            }
        }
    }

    /*
     * Data pro výpis informací o testujících
     * Funkce vrací buď
     *  -null, v případě, že neexistuje tester s nějakými nevyřízenými přihláškami
     *  -nebo pole, kde je v ['select'] výsledek selektu nevyřízených přihlášek pro testování a v
     *   ['jmeno'] a ['prijmeni'] ['id_testera'] je  jméno a příjmení testera
     */

    public function GetPlneTestery($idm) {

        global $PUBLIC_DATABASE_NAME;

        $this->vysledek = array();
        $this->vysledek['select'] = array();
        $this->vysledek['jmeno'] = '';
        $this->vysledek['prijmeni'] = '';
        $this->vysledek['id_testera'] = null;
        // pokud byl testujici vybran, zvol jej, jinak vyber sebe defaultne
        // toto ovlivni default volbu v drop down menu
        if ($idm === null) {
            $this->id_tester = $this->user["id_m"];
        }
        else {
            $this->id_tester = (int) $idm;
        }
        // Dotaz musí brát v úvahu organizátora a spolupracující osobu  - jeden je admin a druhý tester
        $this->tmp = $this->dbsource->Query("
        SELECT DISTINCT id_m, jmeno, prijmeni
            FROM {$PUBLIC_DATABASE_NAME}.m_members
            WHERE id_m IN
                (SELECT DISTINCT id_org
                    FROM {$PUBLIC_DATABASE_NAME}.www_form_1
                    WHERE (typ_testu LIKE 'test' OR typ_testu LIKE '1' OR typ_testu LIKE '2')
                       AND stav NOT IN (-1,1)) " .
                "ORDER BY prijmeni");
        if ($this->dbsource->getNumRows($this->tmp) == 0) {
            // pokud nejsou žádné nevyřízené přihlášky pro testování, pak vrací prázdnou položku
            $this->vysledek['id_testera'] = $this->id_tester;
            return $this->vysledek;
        }
        else {
            while ($row = $this->dbsource->FetchArray($this->tmp)) {
                $this->vysledek['select'][] = $row;
                if ($row["id_m"] == $this->id_tester) {
                    $this->vysledek['jmeno'] = $row['jmeno'];
                    $this->vysledek['prijmeni'] = $row['prijmeni'];
                    $this->vysledek['id_testera'] = $this->id_tester;
                }
            }
            if (!isset($this->vysledek['id_testera'])) {
                $this->vysledek['jmeno'] = $this->user['jmeno'];
                $this->vysledek['prijmeni'] = $this->user['prijmeni'];
                $this->vysledek['id_testera'] = $this->user['id_m'];
            }
            return $this->vysledek;
        }
    }

    /*
     * Funkce vrací seznam přihlášených pro testování
     * pohled testera
     */
    public function GetPrihlasene()
    {
        global $PUBLIC_DATABASE_NAME;

        if (isset($this->vysledek)) {
            unset($this->vysledek);
        }
        $this->vysledek = array();

        /*
        Toto query ma k 2021-10-26 jeden zasadni problem.

        Nelze rozlisit 2 zasadne rozdilne situace:
            a) dotycny uplatnil poukaz z drivejsi platby kartou
               (uspesne zaplatil, ale puvodni termin byl zrusen a hlasi se na nahradni),
               tj. ma zaplaceno.
            b) dotycny platil kartou poprve a platba selhala,
               tj. nema zaplaceno.

        Oboji se barvi jako zelena radka zaplacena kartou bez dalsi informace.

        Informace o platbe kartou se bere z m_poukazy_na_test, kde je priznak,
        ze bylo placeno kartou. Pro kontrolu se pak saha do tabulky payments,
        kde jsou data z platebni brany.

        Nicmene soucasna logika se snazi propojit tyto informace z brany (payments)
        a poukazu m_poukazy_na_test pres pole customId,
        kde je ale id *predchozi* prihlasky (tj. z www_form_1), nikoliv poukazu,
        takze neni mozne ji sparovat s novou prihlaskou, ktera ma ID jine.

        Pro opakovane pouziti v tabulce payments neni nalezen zadny zaznam
        o pltabe pro poukaz z m_poukazy_na_test a danou prihlasku.

        Pokud neni zadna platba kartou uspesna, tak ale v nejakem pripade stejne
        vznikne zaznam v tabulce m_poukazy_na_test s priznamekm platba kartou,
        ale neni k nemu uspesny zaznam v tabuklce payments
        (pouze neuspesny, ale ty se filtruji).

        V tomto dotaze to pak vypada uplne stejne. Mame v tabulce m_poukazy_na_test indikaci,
        ze se jedna o platbu kartou, ale k dane prihlasce se zadna uspesna platba kartou
        v tabulce payments nenajde.

        Idealni reseni by bylo upravit datovy model a/nebo logiku zpracovani:

        a)  jako customId by se na branu neposilal kdo prihlasky, ale kod poukazu (pokud to jde),
            diky tomu by se pak pri kazdem pouziti poukazu dala dohledat asociovana platba
            v tabulce payments a overit jeji stav. Problem zrejme je, ze id zaznamu
            pro tabulku m_poukazy_na_test neni pri zapoceti platby znamo a nelze jej tak na
            branu predat.

        b)  Nebo upravit logiku zpracovani plateb kartou tak, aby se informace do tabulky
            m_poukazy_na_test zapsala pouze, pokud platba uspesne probehne.
            Nyni se totiz stane, ze pri urcitych selhanich platby uzivatel dostane success page
            email potvrzujici platbu a zaznam v tabulce m_poukazy_na_test se vytvori.
            To je celkove bota a pokud by slo opravit chybne pristani na success page,
            tak by to vyresilo problem a jine dalsi drobnosti.

        c)  V "poukazu" bude nejak uvedeno, ze se jedna o znovu reaktivovany poukaz.
            Diky tomu budeme vedet, ze to je special case, kterou budeme resit jinak.

        Backup reseni je upravit logiku SQL nize tak, aby se v dotazu vzdy posilala
        souhrnna informace o vsech platbach kartou. Diky tomu, by pak dotaz vedel,
        ze zaznam v payments existuje, ale je neuspesny a toho by indikoval.

        Tabulky ale nelze pouze joinnout, to by udelalo duplikaty. Musi tam byt
        nejaky souhrnny selekt, ze ktereho vzdy vyleze prave 1 radek.
        */

        $query = "
            SELECT customId, amount, COUNT(*) AS attempts, SUM(IF(payments.status = 'paid', 1, 0)) AS paid
              FROM {$PUBLIC_DATABASE_NAME}.payments
              JOIN www_form_1 ON (www_form_1.id_z = payments.customId AND www_form_1.stav NOT IN (-1, 1, 2))
              GROUP BY customId;
         ";
         
        $payments = [];
        $this->tmp = $this->dbsource->Query($query);
        while ($row = $this->dbsource->FetchAssoc($this->tmp)) {
            $payments[$row["customId"]] = $row;
        }
        
        $query = "
            SELECT
                www_form_1.*,
                DATE_FORMAT(www_form_1.datum, '%e.%c.%Y %k:%i') vlozeno,
                m_poukazy_na_test.kod poukaz,
                m_poukazy_na_test.typ typ_poukazu,
                /* toto bere informaci o platbe kartou ze zaznamu poukazu */
                COALESCE(m_poukazy_na_test.platba_kartou, 0) platba_kartou,
                m_typy_poukazu_na_test.nazev typ_poukazu
            FROM
                {$PUBLIC_DATABASE_NAME}.www_form_1
                JOIN {$PUBLIC_DATABASE_NAME}.mc_akce
                  ON (www_form_1.id_a = mc_akce.id_a AND stav NOT IN (-1, 1, 2) AND (mc_akce.id_owner = {$this->id_tester} OR mc_akce.id_editor = {$this->id_tester}))
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.m_poukazy_na_test
                  ON (www_form_1.id_z = m_poukazy_na_test.id_prihlasky)
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.m_typy_poukazu_na_test
                  ON (m_typy_poukazu_na_test.typ = m_poukazy_na_test.typ)
                /* joinujeme se souhrnnym selektem vsech asociovanych plateb */
            WHERE 
                typ_testu LIKE 'test' OR typ_testu LIKE '1' OR typ_testu LIKE '2'
            ORDER BY
                mc_akce.date_start, mc_akce.id_a,   /* doba testovani, id testovani */
                cast(www_form_1.vek as unsigned), www_form_1.datum;  /* vek testovaneho, datum prihlaseni */
         ";
         
        $this->tmp = $this->dbsource->Query($query);
 
        while ($row = $this->dbsource->FetchAssoc($this->tmp)) {
            $id_z = $row["id_z"];
            $payment_rec = isset($payments[$id_z]) ? $payments[$id_z] : ["paid" => null, "attempts" => null, "amount" => null];
            list($row["stav_platby"], $row["attempts"], $row["castka"]) = [$payment_rec["paid"], $payment_rec["attempts"], $payment_rec["amount"]];
            $this->vysledek[] = $row;
        }
        
        return $this->vysledek;
    }

    /**
     * @param int $id_action
     * @return array
     */
    public function GetPrihlaseneProTisk($id_action)
    {
        global $PUBLIC_DATABASE_NAME;

        $id_action = intval($id_action);
        $out = array();

        $query = "
            SELECT
                wf.*,
                DATE_FORMAT(wf.datum, '%e.%c.%Y %k:%i') vlozeno,
                m_poukazy_na_test.kod poukaz,
                COALESCE(m_poukazy_na_test.platba_kartou, 0) platba_kartou,
                m_typy_poukazu_na_test.nazev typ_poukazu,
                payments.status stav_platby,
                payments.amount castka
            FROM {$PUBLIC_DATABASE_NAME}.www_form_1 wf
                JOIN {$PUBLIC_DATABASE_NAME}.mc_akce a
                  ON (a.id_a = wf.id_a)
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.m_poukazy_na_test
                  ON (m_poukazy_na_test.id_prihlasky = wf.id_z)
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.m_typy_poukazu_na_test
                  ON (m_typy_poukazu_na_test.typ = m_poukazy_na_test.typ)
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.payments
                  ON (wf.id_z = payments.customId AND payments.status = 'paid')
            WHERE (a.id_owner = {$this->id_tester} || a.id_editor = {$this->id_tester} )
                AND wf.id_a = {$id_action}
                AND (typ_testu LIKE 'test' OR typ_testu LIKE '1' OR typ_testu LIKE '2')
                AND wf.stav NOT IN (-1, 1, 2)
            ORDER BY
                cast(wf.vek as unsigned), wf.jmeno, wf.datum  /* vek testovaneho, datum prihlaseni */
        ";

        $temp = $this->dbsource->Query($query);

        while ($row = $this->dbsource->FetchAssoc($temp)) {
            $out[] = $row;
        }

        return $out;
    }

    /*
     * vrací seznam zpracovaných otestovaných, vrácených adminem testování
     */
    public function GetVracene() {

        global $SECURE_DATABASE_NAME;

        if (isset($this->vysledek)) {
            unset($this->vysledek);
        }
        $this->vysledek = array();
        $this->vysledek['select'] = array();

        $query = "
            SELECT
                c_m_otestovan.*,
                DATE_FORMAT(www_form_1.datum, '%e.%c.%Y %k:%i') AS vlozeno,
                www_form_1.termin,www_form_1.poznamka,
                DATE_FORMAT(c_m_otestovan.datum_narozeni, '%e.%c.%Y %k:%i') AS narozeni,
                www_form_1.id_a
            FROM
                {$SECURE_DATABASE_NAME}.c_m_otestovan,
                {$this->databasename}.www_form_1
            WHERE c_m_otestovan.id_z=www_form_1.id_z AND
                (id_testujiciho = {$this->id_tester})
                AND www_form_1.stav = 2
            ORDER BY
                id_a, datum
        ";

        $this->tmp = $this->dbsource->Query($query);

        while ($row = $this->dbsource->FetchArray($this->tmp)) {
            $this->vysledek['select'][] = $row;
        }
        return $this->vysledek;
    }

    /**
     * Načte data uživatele ze secure databáze podle $c_id_m
     * použito pro tisk sdělení výsledků testu
     *
     * @param AES $aes_crypt - objekt šifrování
     * @param int $c_id_m - položka mensasec.c_m_members.c_id_m - identifikátor člena, na kterého se dotazujeme
     * @param int $cisloTestu - poradí testu v seznamu
     * @return array - všechna data o členovi a všechny datumy testů pro tisk a dešifrované iq a percentily
     */
    public function ReadUserData($aes_crypt, $c_id_m, $cisloTestu)
    {
        $query = "
        SELECT *, 
        DATE_FORMAT(datumnar, '%e. %c. %Y') datum_nar,
        DATE_FORMAT(datumtestu1, '%e. %c. %Y') datum_testu1,
        DATE_FORMAT(datumtestu2, '%e. %c. %Y') datum_testu2,
        DATE_FORMAT(datumtestu3, '%e. %c. %Y') datum_testu3,
        DATE_FORMAT(FROM_DAYS(DATEDIFF(CURDATE(), datumnar)), '%Y') vek,
        DATE_FORMAT(mitch_datum_1, '%e. %c. %Y') mitch_datum_1,
        DATE_FORMAT(mitch_datum_2, '%e. %c. %Y') mitch_datum_2,
        DATE_FORMAT(mitch_datum_3, '%e. %c. %Y') mitch_datum_3
        FROM mensasec.c_m_members
        WHERE c_id_m = {$c_id_m}
        ";

        $radek = $this->dbsource->FetchArray($this->dbsource->Query($query));

        if ($cisloTestu == 1) {
            $radek['iq'] = $aes_crypt->decrypt($radek['iq1']);
            $radek['per'] = $aes_crypt->decrypt($radek['per1']);
            $radek['datum_testu'] = $radek['datum_testu1'];

            $radek['mitch_odpovedi'] = $aes_crypt->decrypt($radek['mitch_odpovedi_1']);
            $radek['mitch_iq'] = $aes_crypt->decrypt($radek['mitch_iq_1']);
            $radek['mitch_datum'] = $radek['mitch_datum_1'];
        }
        if ($cisloTestu == 2) {
            $radek['iq'] = $aes_crypt->decrypt($radek['iq2']);
            $radek['per'] = $aes_crypt->decrypt($radek['per2']);
            $radek['datum_testu'] = $radek['datum_testu2'];

            $radek['mitch_odpovedi'] = $aes_crypt->decrypt($radek['mitch_odpovedi_2']);
            $radek['mitch_iq'] = $aes_crypt->decrypt($radek['mitch_iq_2']);
            $radek['mitch_datum'] = $radek['mitch_datum_2'];
        }
        if ($cisloTestu == 3) {
            $radek['iq'] = $aes_crypt->decrypt($radek['iq3']);
            $radek['per'] = $aes_crypt->decrypt($radek['per3']);
            $radek['datum_testu'] = $radek['datum_testu3'];

            $radek['mitch_odpovedi'] = $aes_crypt->decrypt($radek['mitch_odpovedi_3']);
            $radek['mitch_iq'] = $aes_crypt->decrypt($radek['mitch_iq_3']);
            $radek['mitch_datum'] = $radek['mitch_datum_3'];
        }
        if ($radek['typ'] == 2) {
            $prepocet = $this->standardPercentily($radek['per'], $radek['datumnar'], $radek['datum_testu']);
            $radek['per'] = $prepocet['per'];
            $radek['slovo_perc'] = $prepocet['slovo_perc'];
        }

        return $radek;
    }

    /**
     * Načte data uživatele z mensasec.c_m_members podle $c_id_m pro možnosti tisku všech dostupných testů
     *
     * @param int $c_id_m - položka mensasec.c_m_members.c_id_m - identifikátor člena, na kterého se dotazujeme
     * @return array (typ člena, datumy všech 3 možných testů)
     */
    public function ReadDataTestu($c_id_m)
    {
        $query = "
        SELECT typ, 
        DATE_FORMAT(datumtestu1, '%d.%m.%Y') datum_testu1, 
        DATE_FORMAT(datumtestu2, '%d.%m.%Y') datum_testu2, 
        DATE_FORMAT(datumtestu3, '%d.%m.%Y') datum_testu3,
        DATE_FORMAT(mitch_datum_1, '%d.%m.%Y') mitch_datum_1, 
        DATE_FORMAT(mitch_datum_2, '%d.%m.%Y') mitch_datum_2, 
        DATE_FORMAT(mitch_datum_3, '%d.%m.%Y') mitch_datum_3
        FROM mensasec.c_m_members 
        WHERE c_id_m = {$c_id_m}";

        return $this->dbsource->FetchArray($this->dbsource->Query($query));
    }

    /*
     * Načte data z odeslané přihlášky
     */

    /**
     * @param CryptoInterface $aes_crypt
     * @return array|null
     */
    public function ReadPrihlasku($aes_crypt) {
        global $SECURE_DATABASE_NAME, $PUBLIC_DATABASE_NAME;

        $vyber_akci = $this->inglob->getString('vyber_akci');

        if (is_null($vyber_akci) || $vyber_akci != 'Zapsat jako otestovaného') {
            $this->prihlaska = array();
            $this->prihlaska['err'] = 'new';
            $this->prihlaska['id_testujiciho'] = $this->inglob->getInteger('id_testera', $this->user['id_m']);
            $this->prihlaska['id_z'] = $this->inglob->getInteger('id_z', 0);  // id_z je id přihlášky na akci

            // Tester je buď přenesen v GLOBALS nebo je stejný jako uživatel
            // nacti data z prihlasky na testovani dane $id_z
            if ($this->prihlaska['id_z'] > 0) {
                // TODO formátovat datum až při výpisu nebo ve formuláři standardizovaným formatterem, ne u zdroje dat (sql)

                // najde danou přihlášku pro již jednou zadaného testovaného a pravděpodobně vráceného adminem
                $query = "
                    SELECT c_m_otestovan.*, DATE_FORMAT(datum_narozeni, '%d.%m.%Y') datumnar, DATE_FORMAT(datum_testu, '%d.%m.%Y') datumtestu, www_form_1.poznamka, www_form_1.vek
                    FROM {$SECURE_DATABASE_NAME}.c_m_otestovan
                    LEFT JOIN {$PUBLIC_DATABASE_NAME}.www_form_1 ON www_form_1.id_z = c_m_otestovan.id_z
                    WHERE c_m_otestovan.id_z = {$this->prihlaska['id_z']}
                ";
                $this->tmp = $this->dbsource->Query($query);

                if ($this->dbsource->getNumRows($this->tmp) > 0) {
                    $this->prihlaska = $this->dbsource->FetchAssoc($this->tmp);
                    $this->prihlaska['err'] = 'new';
                    $this->prihlaska['datum_testu'] = $this->prihlaska['datumtestu'];
                    $this->prihlaska['iq'] = $aes_crypt->decrypt($this->prihlaska["iq"]);
                    $this->prihlaska['per'] = $aes_crypt->decrypt($this->prihlaska["per"]);
                    $this->prihlaska['mitch_odpovedi'] = $aes_crypt->decrypt($this->prihlaska["mitch_odpovedi"]);
                    $this->prihlaska['mitch_iq'] = $aes_crypt->decrypt($this->prihlaska["mitch_iq"]);
                    $this->prihlaska['adresa'] = $this->prihlaska['ulice'];
                    $this->prihlaska['adresa1'] = $this->prihlaska['ulice1'];
                    $this->prihlaska['typ_testu'] = $this->prihlaska['typ'];
                }
                else {
                    //ještě nebyl odeslán k adminovi
                    $query = "
                        SELECT *
                        FROM {$this->databasename}.www_form_1
                        WHERE id_z = {$this->prihlaska['id_z']}
                    ";

                    $this->tmp = $this->dbsource->Query($query);
                    $this->vysledek = $this->dbsource->FetchArray($this->tmp);

                    $ar = explode(" ", $this->vysledek["jmeno"]);

                    $this->prihlaska['jmeno'] = $ar[0];
                    $this->prihlaska['prijmeni'] = trim(@$ar[1] . " " . @$ar[2] . " " . @$ar[3] . " " . @$ar[4]);
                    $this->prihlaska['titul'] = '';
                    $this->prihlaska['titul_za_jmenem'] = '';
                    $this->prihlaska['pohlavi'] = '';
                    $this->prihlaska['trida'] = $this->vysledek["trida"];
                    $this->prihlaska['vek'] = $this->vysledek["vek"];
                    $this->prihlaska['mobil'] = $this->vysledek["telefon"];
                    $this->prihlaska['email'] = $this->vysledek["email"];
                    $this->prihlaska['email_status']=$this->vysledek["email_status"];
                    $this->prihlaska['email_verify_time']=$this->vysledek["email_verify_time"];
                    $this->prihlaska['email_verify_ip']=$this->vysledek["email_verify_ip"];
                    $this->prihlaska['email2'] = $this->vysledek["email2"];
                    $this->prihlaska['email2_status']=$this->vysledek["email2_status"];
                    $this->prihlaska['email2_verify_time']=$this->vysledek["email2_verify_time"];
                    $this->prihlaska['email2_verify_ip']=$this->vysledek["email2_verify_ip"];
                    $this->prihlaska['jmeno_zz'] = $this->vysledek['jmeno_zz'];
                    $this->prihlaska['typ_testu'] = $this->vysledek["typ_testu"];
                    $this->prihlaska['termin'] = $this->vysledek["termin"];
                    $this->prihlaska['poznamka'] = $this->vysledek["poznamka"];
                    $this->prihlaska['datum'] = $this->vysledek["datum"];
                    $this->prihlaska['id_a'] = $this->vysledek["id_a"];
                    $this->prihlaska['id_org'] = $this->vysledek["id_org"];
                    $this->prihlaska['stav'] = $this->vysledek["stav"];
                    $this->prihlaska['adresa'] = $this->vysledek["adresa"];
                    $this->prihlaska['mesto'] = $this->vysledek["mesto"];
                    $this->prihlaska['psc'] = $this->vysledek["psc"];
                    // TODO use real SQL date type in database for datumnar
                    $this->prihlaska['datumnar'] = Dates::addLeadingZerosToPlainDate($this->vysledek["datumnar"]);
                    $this->prihlaska['telefon'] = $this->vysledek["rc"];
                    $this->prihlaska['byt'] = $this->vysledek["cc"];
                    $this->prihlaska['byt1'] = $this->vysledek["variabilni"];
                    $this->prihlaska['adresa1'] = $this->vysledek["vol1"];
                    $this->prihlaska['mesto1'] = $this->vysledek["vol2"];
                    $this->prihlaska['psc1'] = $this->vysledek["vol3"];
                    $this->prihlaska['ip'] = $this->vysledek["ip"];
                    $this->prihlaska['iq'] = '';
                    $this->prihlaska['per'] = '';
                    $this->prihlaska['mitch_odpovedi'] = '';
                    $this->prihlaska['mitch_iq'] = '';
                    $this->prihlaska['certifikat'] = 0;
                    $this->prihlaska['pocet_certifikatu'] = 0;

                    // TODO formátovat datum až při výpisu nebo ve formuláři standardizovaným formatterem, ne u zdroje dat (sql)
                    $query = "
                        SELECT DATE_FORMAT(date_start, '%d.%m.%Y') datum_testu
                        FROM {$PUBLIC_DATABASE_NAME}.mc_akce
                        WHERE id_a = {$this->prihlaska['id_a']}
                    ";

                    $this->tmp = $this->dbsource->Query($query);

                    $this->prihlaska['datum_testu'] = $this->dbsource->getResult($this->tmp, 0, "datum_testu");
                }
            }
            else {
                // zcela nový zápis do dat
                $this->prihlaska['jmeno'] = '';
                $this->prihlaska['prijmeni'] = '';
                $this->prihlaska['trida'] = '';
                $this->prihlaska['vek'] = '';
                $this->prihlaska['mobil'] = '';
                $this->prihlaska['email'] = '';
                $this->prihlaska['email_status']='';
                $this->prihlaska['email_verify_time']='';
                $this->prihlaska['email_verify_ip']='';
                $this->prihlaska['email2'] = '';
                $this->prihlaska['email2_status']='';
                $this->prihlaska['email2_verify_time']='';
                $this->prihlaska['email2_verify_ip']='';
                $this->prihlaska['jmeno_zz'] = '';
                $this->prihlaska['typ_testu'] = 'test';
                $this->prihlaska['termin'] = '';
                $this->prihlaska['poznamka'] = '';
                $this->prihlaska['datum'] = '';
                $this->prihlaska['id_a'] = '';
                $this->prihlaska['id_org'] = '';
                $this->prihlaska['stav'] = '';
                $this->prihlaska['adresa'] = '';
                $this->prihlaska['mesto'] = '';
                $this->prihlaska['psc'] = '';
                $this->prihlaska['datumnar'] = '';
                $this->prihlaska['telefon'] = '';
                $this->prihlaska['byt'] = '';
                $this->prihlaska['byt1'] = '';
                $this->prihlaska['adresa1'] = '';
                $this->prihlaska['mesto1'] = '';
                $this->prihlaska['psc1'] = '';
                $this->prihlaska['ip'] = '';
                $this->prihlaska['mitch_odpovedi'] = '';
                $this->prihlaska['mitch_iq'] = '';
                $this->prihlaska['datum_testu'] = date("d.m.Y");
            }

            if ($this->prihlaska['typ_testu'] == 2) {
                $vyhodnoceni = $this->standardPercentily($this->prihlaska['per'], $this->prihlaska['datumnar'], $this->prihlaska['datum_testu']);

                //$this->prihlaska['per'] = $vyhodnoceni['per'];
                $this->prihlaska['slovo_perc'] = $vyhodnoceni['slovo_perc'];
            }

            return $this->prihlaska;
        }
    }

    /*
     * Kontroluje a odesílá doplněnou přihlášku adminovi
     */

    public function SendPrihlasku($db2, $securedtbname, $aes_crypt) {

        global $SECURE_DATABASE_NAME;

        $vyber_akci = $this->inglob->getString('vyber_akci');

        if (isset($vyber_akci) && ($vyber_akci == 'Zapsat jako otestovaného' || $vyber_akci == 'Zapsat nového otestovaného' || $vyber_akci == 'Ulož' )) {
            $this->prihlaska = array();
            // následuje konverze dat z přihlášky do tvaru pro c_m_otestovan
            $this->prihlaska['err'] = '';
            $this->prihlaska['adm'] = $this->inglob->getInteger('adm', 0);
            $this->prihlaska['id_testujiciho'] = $this->inglob->getInteger('id_testera', $this->user['id_m']);
            $this->prihlaska['id_z'] = $this->inglob->getInteger('id_z', 0);  // id_z je id přihlášky na akci
            $this->prihlaska['titul'] = $this->inglob->getString('titul', '');
            $this->prihlaska['jmeno'] = $this->inglob->getString('jmeno', '');
            $this->prihlaska['prijmeni'] = $this->inglob->getString('prijmeni', '');
            $this->prihlaska['titul_za_jmenem'] = $this->inglob->getString('titul_za_jmenem', '');
            $this->prihlaska['pohlavi'] = $this->inglob->getString('pohlavi', '');
            $this->prihlaska['trida'] = $this->inglob->getString('trida', '');
            $this->prihlaska['vek'] = $this->inglob->getString('vek', '');
            $this->prihlaska['mobil'] = $this->inglob->getString('mobil', '');

            $this->prihlaska['email'] = $this->inglob->getString('email', '');
            $this->prihlaska['email_status'] = $this->inglob->getString("email_status", "");
            $this->prihlaska['email_verify_time'] = $this->inglob->getString("email_verify_time", "");
            $this->prihlaska['email_verify_ip'] = $this->inglob->getString("email_verify_ip", "");
            $this->prihlaska['email2'] = $this->inglob->getString("email2", "");
            $this->prihlaska['email2_status'] = $this->inglob->getString("email2_status", "");
            $this->prihlaska['email2_verify_time'] = $this->inglob->getString("email2_verify_time", "");
            $this->prihlaska['email2_verify_ip'] = $this->inglob->getString("email2_verify_ip", "");
            $this->prihlaska['jmeno_zz'] = $this->inglob->getString("jmeno_zz", "");

            $this->prihlaska['typ_testu'] = $this->inglob->getString('typ_testu', 'test');
            $this->prihlaska['termin'] = $this->inglob->getString('termin', '');
            $this->prihlaska['poznamka'] = $this->inglob->getString('vzkaz', '');
            $this->prihlaska['datum'] = $this->inglob->getString('datum', '');
            $this->prihlaska['id_a'] = $this->inglob->getInteger('id_a', 0);
            $this->prihlaska['id_org'] = $this->inglob->getString('id_org', '');
            $this->prihlaska['stav'] = $this->inglob->getInteger('stav', 0);
            $this->prihlaska['adresa'] = $this->inglob->getString('adresa', '');
            $this->prihlaska['mesto'] = $this->inglob->getString('mesto', '');
            $this->prihlaska['psc'] = $this->inglob->getString('psc', '');
            $this->prihlaska['datumnar'] = $this->inglob->getDatum('datumnar', '');
            $this->prihlaska['telefon'] = $this->inglob->getString('telefon', '');
            $this->prihlaska['mobil'] = $this->inglob->getString('mobil', '');
            $this->prihlaska['byt'] = $this->inglob->getString('byt', '');
            $this->prihlaska['byt1'] = $this->inglob->getString('byt1', '');
            $this->prihlaska['adresa1'] = $this->inglob->getString('adresa1', '');
            $this->prihlaska['mesto1'] = $this->inglob->getString('mesto1', '');
            $this->prihlaska['psc1'] = $this->inglob->getString('psc1', '');
            $this->prihlaska['datum_testu'] = $this->inglob->getDatum('datum_testu', '');
            $this->prihlaska['certifikat'] = $this->inglob->getInteger('certifikat', 0);
            $this->prihlaska['pocet_certifikatu'] = $this->inglob->getInteger('pocet_certifikatu', 0);
            $this->prihlaska['iq'] = $this->inglob->getString('iq', ''); // musi byt taky string, protoze tam mohou byt i texty pod
            $this->prihlaska['per'] = $this->inglob->getString('per', '');
            $this->prihlaska['mitch_odpovedi'] = $this->inglob->getString('mitch_odpovedi', '');
            $this->prihlaska['mitch_iq'] = $this->inglob->getString('mitch_iq', '');

            ////////////////////////////////////////////////////////////////////
            // overeni hodnot
            //

            // TODO přesunout jinam / validátory?
            function validateDate($form_date, $field_title, $min_date, $max_date) {
                if (!preg_match("/^\d{2}\.\d{2}\.\d{4}$/", $form_date, $matches)) {
                    return "{$field_title} musí být ve formátu dd.mm.yyyy. <br />";
                }

                list($day, $month, $year) = explode(".", $form_date);
                $date = "{$year}-{$month}-{$day}";

                // TODO use intl date formatter
                if ($date < $min_date) {
                    return "{$field_title} musí být větší než " . date("d.m.Y", strtotime($min_date)) . ". <br />";
                }
                if ($date > $max_date) {
                    return "{$field_title} musí být menší než aktuální datum. <br />";
                }

                return "";
            }

            /* proměnné pro kontroly napříč poli a mitch */
            $has_raven_iq = mb_strlen($this->prihlaska['iq']) > 0;
            $has_raven_percentile = mb_strlen($this->prihlaska['per']) > 0;

            $has_mitch_odpovedi = mb_strlen($this->prihlaska['mitch_odpovedi']) > 0;
            $has_mitch_iq = mb_strlen($this->prihlaska['mitch_iq']) > 0;

            $has_mitch = $has_mitch_odpovedi || $has_mitch_iq;
            $has_raven = $has_raven_iq || $has_raven_percentile;

            // Test povinných položek
            if ($this->prihlaska['typ_testu'] == 'test') {
                $this->prihlaska['err'] .= "Musíte zadat typ testu.<br />";
            }
            if (!$this->prihlaska['jmeno']) {
                $this->prihlaska['err'] .= "Musíte zadat jméno otestovaného.<br />";
            }
            if (!$this->prihlaska['prijmeni']) {
                $this->prihlaska['err'] .= "Musíte zadat příjmení otestovaného.<br />";
            }
            if (!$this->prihlaska['mesto']) {
                $this->prihlaska['err'] .= "Musíte zadat město otestovaného.<br />";
            }

            $this->prihlaska['err'] .= validateDate(
                $this->prihlaska['datum_testu'],
                "Datum testu",
                MIN_DATE_OF_TEST,
                date("Y-m-d")
            );

            $this->prihlaska['err'] .= validateDate(
                $this->prihlaska['datumnar'],
                "Datum narození",
                MIN_DATE_OF_BIRTH,
                date("Y-m-d")
            );

            if (!in_array($this->prihlaska['pohlavi'], [1, 2])) {
                $this->prihlaska['err'] .= "Musíte zadat pohlaví otestovaného.<br />";
            }

            /* raven nebo mitch pro dětskou mensu */
            if ($this->prihlaska['typ_testu'] == 2 && !$has_raven && !$has_mitch) {
                $this->prihlaska['err'] .= "Pro dětskou mensu musí být vyplněn buď Raven test nebo Mitch test.<br />";
            }

            // vypocti vek pro dalsi vyuziti
            $vek = $this->dejVek($this->prihlaska['datumnar'], $this->prihlaska['datum_testu']);

            // hodnota IQ pro dospělý test
            if ($this->prihlaska['typ_testu'] == 1 && !preg_match ("/^0|(\d{2,3})|(pod \d{2})$/", $this->prihlaska['iq'])) {
                $this->prihlaska['err'] .= "IQ dospělého bylo špatně zadáno.<br>";
            }

            // ověř rozmezí dospělého IQ
            if ($this->prihlaska['typ_testu'] == 1 && preg_match ("/^\d{2,3}$/", $this->prihlaska['iq']) && ($this->prihlaska['iq'] < 72 || $this->prihlaska['iq'] > 190)) {
                $this->prihlaska['err'] .= "IQ dospělého je mimo povolený interval.<br>";
            }

            // hodnota v poli IQ pro děti (počet otázek), IQ je uloženo jako integer
            if ($this->prihlaska['typ_testu'] == 2 && $has_raven && (!preg_match ("/^0|(\d{1,2})$/", $this->prihlaska['iq']))) {
                $this->prihlaska['err'] .= "Pro děti zadejte do pole IQ počet správných otázek nebo 0 pro nevalidní test.<br>";
            }

            // male dite
            if ($vek < MAX_VEK_MALE_DITE && ($this->prihlaska['iq'] > 36)) {
                $this->prihlaska['err'] .= "Pro děti ve věku {$vek} je maximální počet odpovědí 36.<br>";
            }

            // velke dite
            if ($this->prihlaska['typ_testu'] == 2 && ($this->prihlaska['iq'] > 60)) {
                $this->prihlaska['err'] .= "Pro děti ve ve věku nad " . MAX_VEK_MALE_DITE . " let je maximální počet odpovědí 60.<br>";
            }

            // Typ testu - vyjimka TK: dospely test lze zadat uz od 13 let
            if ($vek < (MAX_VEK_DETSKY_TEST - 1) && $this->prihlaska['typ_testu'] != 2) {
                $this->prihlaska['err'] .= "Vybrán špatný typ testu, věku " . $this->dejVek($this->prihlaska['datumnar'], $this->prihlaska['datum_testu']) . " let by odpovídal dětský test.<br>";
            }

            // typ testu - dospělému nelze zadat dětský test (výjimka je tam opět přesah 1 rok)
            if ($vek > (MAX_VEK_DETSKY_TEST) && $this->prihlaska['typ_testu'] != 1) {
                $this->prihlaska['err'] .= "Vybrán špatný typ testu, věku " . $this->dejVek($this->prihlaska['datumnar'], $this->prihlaska['datum_testu']) . " let by odpovídal dospělý test.<br>";
            }

            // dospělý nemůže mít percentil
            if ($this->prihlaska['typ_testu'] == 1 && mb_strlen($this->prihlaska['per']) > 0) {
                $this->prihlaska['err'] .= "Dospělý nemůže mít zadaný percentil.<br />";
            }
            // dospělý nemůže mít mitch
            if ($this->prihlaska['typ_testu'] == 1 && $has_mitch) {
                $this->prihlaska['err'] .= "Dospělý nemůže mít zadaný Mitch test.<br />";
            }

            // TK obecna kontrola, protoze dalsi vetve jsou derave kvuli prekryvu
            // dítě musí mít zadaný percentil
            if ($this->prihlaska['typ_testu'] == 2 && $has_raven && !preg_match ("/^(\d{2,3})|(pod 11)|(\d{1,2} ?- ?\d{2,3})$/", $this->prihlaska['per'])) {
                $this->prihlaska['err'] .= "Dítě musí mít zadaný percentil ve správném tvaru.<br>";
            }

            // pro dětskou mensu do 7 let včetně
            // zadání percentilu male deti (mene nez 7 let), 7 rok vyhazujeme, protoze tam uz mohou byt velke testy
            if (mb_strlen($this->prihlaska['datumnar']) > 4 && $this->prihlaska['typ_testu'] == 2 && $vek < MAX_VEK_MALE_DITE
                && !preg_match ("/^(0 ?- ?25)|(26 ?- ?75)|(76 ?- ?90)|(91 ?- ?98)|(99 ?- ?100)$/", $this->prihlaska['per']) && !$has_mitch) {
                //$percentilytest = explode("-", $this->prihlaska['per']);
                //if ((!isset($percentilytest[1]) || $percentilytest[1] > 100)) {
                $this->prihlaska['err'] .= "Špatně zadaný percentil pro dítě ve věku do " . MAX_VEK_MALE_DITE . " let (včetně), zadejte pásmo.<br />";
                //}
            }

            // zadani percentilu velke deti
            // TK oprava: neni horni omezeni veku, je to dano typem testu: && $this->dejVek($this->prihlaska['datumnar'], $this->prihlaska['datum_testu']) <= 14
            if (mb_strlen($this->prihlaska['datumnar']) > 4 && $this->prihlaska['typ_testu'] == 2 && $vek > (MAX_VEK_MALE_DITE + 1)) {
                // pro dětskou mensu  8-13 let včetně
                if ($has_raven && $this->check_detska_mensa_percentile_8_13($this->prihlaska['per'])) {
                    $this->prihlaska['err'] .= "Špatně zadaný percentil pro dítě ve věku nad " . MAX_VEK_MALE_DITE . " let, zadejte hodnotu.<br />";
                }
            }
            // rok 8 je zaměrně z testu percentilu vypuštěn, mohou tam být obě varianty (na to by bylo třeba kontrolu úplně předělat).

            /* mitch validace, pouze dětská mensa */
            if ($this->prihlaska['typ_testu'] == 2 && $has_mitch) {
                if ($has_raven) {
                    $this->prihlaska['err'] .= "Pro dětskou mensu musí být vyplněn buď Raven test nebo Mitch test (ne oba).<br />";
                }
                else {
                    if ($has_mitch_odpovedi && !$this->is_mitch_odpovedi_ok($this->prihlaska['mitch_odpovedi'])) {
                        $this->prihlaska['err'] .= "Mitch: Počet odpovědí musí být číslo 0-44.<br />";
                    }
                    if ($has_mitch_iq && !$this->is_mitch_iq_ok($this->prihlaska['mitch_iq'])) {
                        $this->prihlaska['err'] .= "Mitch: IQ musí být číslo 0, číslo 80-145, text &quot;více než 134-145&quot; nebo text &quot;pod &quot; a číslo 80-83.<br />";
                    }
                }
            }

            // certifikat
            if (!is_numeric($this->prihlaska['certifikat']) || (intval($this->prihlaska['certifikat']) % \intranet\platby\ucet\Cenik::CERTIFIKAT) !== 0) {
                $this->prihlaska['err'] .= "Špatně zadaná částka certifikátu.<br>";
            }
            // pokud je cena je potreba zadat i počet
            if (!empty($this->prihlaska['certifikat']) && empty($this->prihlaska["pocet_certifikatu"])) {
                $this->prihlaska['err'] .= "Musíte zadat počet certifikátů.<br>";
            }
            // email
            if (!filter_var($this->prihlaska['email'], FILTER_VALIDATE_EMAIL)) {
                $this->prihlaska['err'] .= "Špatně zadaný email, vyplňte 1 email nebo nechte prázdné.<br>";
            }


            // pokud vstupuje z čistého formuláře
            if ($this->prihlaska['id_z'] == 0) {
                $this->dotazsql = "INSERT INTO {$this->databasename}.www_form_1 SET
                    stav=1,
                    datum = NOW(),
                    id_org=" . $this->user["id_m"] . ",
                    ip='0.0.0.0'
                    ";
                $this->dbsource->Query($this->dotazsql);
                $this->prihlaska['id_z'] = $this->dbsource->getInsertId();
            }

            if ($this->prihlaska['err'] == "") {
                $this->tmp = $db2->Query("
                    SELECT *
                    FROM {$SECURE_DATABASE_NAME}.c_m_otestovan
                    WHERE id_z = {$this->prihlaska['id_z']}
                ");

                $jmeno_zz_query_part = $this->prihlaska['jmeno_zz'] ? " '".addslashes($this->prihlaska['jmeno_zz'])."' " : " NULL ";

                if ($this->dbsource->getNumRows($this->tmp) > 0) {
                    $this->dotazsql = "UPDATE {$SECURE_DATABASE_NAME}.c_m_otestovan
                        SET typ=" . $this->prihlaska['typ_testu'] . ",
                        titul='" . addslashes($this->prihlaska['titul']) . "',
                        jmeno='" . addslashes($this->prihlaska['jmeno']) . "',
                        prijmeni='" . addslashes($this->prihlaska['prijmeni']) . "',
                        titul_za_jmenem='" . addslashes($this->prihlaska['titul_za_jmenem']) . "',
                        pohlavi='" . addslashes($this->prihlaska['pohlavi']) . "',
                        datum_testu='" . write_date_sql($this->prihlaska['datum_testu']) . "',
                        datum_narozeni='" . write_date_sql($this->prihlaska['datumnar']) . "',
                        byt='" . addslashes($this->prihlaska['byt']) . "',
                        ulice='" . addslashes($this->prihlaska['adresa']) . "',
                        mesto='" . addslashes($this->prihlaska['mesto']) . "',
                        psc='" . addslashes($this->prihlaska['psc']) . "',
                        byt1='" . addslashes($this->prihlaska['byt1']) . "',
                        ulice1='" . addslashes($this->prihlaska['adresa1']) . "',
                        mesto1='" . addslashes($this->prihlaska['mesto1']) . "',
                        psc1='" . addslashes($this->prihlaska['psc1']) . "',
                        email='" . addslashes($this->prihlaska['email']) . "',
                        telefon='" . addslashes($this->prihlaska['telefon']) . "',
                        mobil='" . addslashes($this->prihlaska['mobil']) . "',
                        certifikat='" . addslashes($this->prihlaska['certifikat']) . "',
                        pocet_certifikatu='" . addslashes($this->prihlaska['pocet_certifikatu']) . "',
                        id_testujiciho=" . $this->prihlaska['id_testujiciho'] . ",
                        iq='" . $aes_crypt->Encrypt($this->prihlaska['iq']) . "',
                        per='" . $aes_crypt->Encrypt($this->prihlaska['per']) . "',
                        stav=0,
                        vzkaz='" . (empty($this->prihlaska['vzkaz'])?"":addslashes($this->prihlaska['vzkaz'])) . "',
                        email_status=" . (empty($this->prihlaska['email_status']) ? 0 : $this->prihlaska['email_status']) . ",
                        email_verify_time='" . addslashes($this->prihlaska['email_verify_time']) . "',
                        email_verify_ip='" . addslashes($this->prihlaska['email_verify_ip']) . "',
                        email2='" . addslashes($this->prihlaska['email2']) . "',
                        email2_status=" . (empty($this->prihlaska['email2_status']) ? 0 : $this->prihlaska['email2_status']) . ",
                        email2_verify_time='" . addslashes($this->prihlaska['email2_verify_time']) . "',
                        email2_verify_ip='" . addslashes($this->prihlaska['email2_verify_ip']) . "',
                        jmeno_zz = {$jmeno_zz_query_part},
                        mitch_odpovedi = '" . $aes_crypt->Encrypt($this->prihlaska['mitch_odpovedi']) . "',
                        mitch_iq = '" . $aes_crypt->Encrypt($this->prihlaska['mitch_iq']) . "'

                        WHERE id_z=" . $this->prihlaska['id_z'];
                }
                else {
                    $this->dotazsql = "INSERT INTO {$SECURE_DATABASE_NAME}.c_m_otestovan (
                        typ, titul, jmeno, prijmeni, titul_za_jmenem, pohlavi, datum_testu, 
                        datum_narozeni, byt, ulice, mesto, psc, byt1, ulice1, mesto1, psc1,
                        email, telefon, mobil, certifikat, pocet_certifikatu, id_testujiciho, iq, per, id_z, vzkaz,
                        email_status, email_verify_time, email_verify_ip,
                        email2, email2_status, email2_verify_time, email2_verify_ip,
                        jmeno_zz, mitch_odpovedi, mitch_iq
                    )
                    VALUES
                    (" . $this->prihlaska['typ_testu'] . ",
                        '" . addslashes($this->prihlaska['titul']) . "',
                        '" . addslashes($this->prihlaska['jmeno']) . "',
                        '" . addslashes($this->prihlaska['prijmeni']) . "',
                        '" . addslashes($this->prihlaska['titul_za_jmenem']) . "',
                        '" . addslashes($this->prihlaska['pohlavi']) . "',
                        '" . write_date_sql($this->prihlaska['datum_testu']) . "',
                        '" . write_date_sql($this->prihlaska['datumnar']) . "',
                        '" . addslashes($this->prihlaska['byt']) . "',
                        '" . addslashes($this->prihlaska['adresa']) . "',
                        '" . addslashes($this->prihlaska['mesto']) . "',
                        '" . addslashes($this->prihlaska['psc']) . "',
                        '" . addslashes($this->prihlaska['byt1']) . "',
                        '" . addslashes($this->prihlaska['adresa1']) . "',
                        '" . addslashes($this->prihlaska['mesto1']) . "',
                        '" . addslashes($this->prihlaska['psc1']) . "',
                        '" . addslashes($this->prihlaska['email']) . "',
                        '" . addslashes($this->prihlaska['telefon']) . "',
                        '" . addslashes($this->prihlaska['mobil']) . "',
                        '" . addslashes($this->prihlaska['certifikat']) . "',
                        '" . addslashes($this->prihlaska['pocet_certifikatu']) . "',
                        " . $this->prihlaska['id_testujiciho'] . ",
                        '" . $aes_crypt->Encrypt($this->prihlaska['iq']) . "',
                        '" . $aes_crypt->Encrypt($this->prihlaska['per']) ."',
                        " . $this->prihlaska['id_z'] . ",
                        '" . (empty($this->prihlaska['vzkaz']) ? "" : addslashes($this->prihlaska['vzkaz'])) . "',
                        " . (empty($this->prihlaska['email_status']) ? 0 : $this->prihlaska['email_status']) . ",
                        " . (empty($this->prihlaska['email_verify_time']) ? "NULL" : "'".addslashes($this->prihlaska['email_verify_time'])."'") . ",
                        '" . addslashes($this->prihlaska['email_verify_ip']) . "',
                        '" . addslashes($this->prihlaska['email2']) . "',
                        " . (empty($this->prihlaska['email2_status'])?0:$this->prihlaska['email2_status']) .  ",
                        " . (empty($this->prihlaska['email2_verify_time'])?"NULL":"'".addslashes($this->prihlaska['email2_verify_time'])."'") . ",
                        '" . addslashes($this->prihlaska['email2_verify_ip']) . "',
                        {$jmeno_zz_query_part},
                        '" . $aes_crypt->Encrypt($this->prihlaska['mitch_odpovedi']) . "',
                        '" . $aes_crypt->Encrypt($this->prihlaska['mitch_iq']) . "'
                    ) ";
                }

/*
                $score = "120";
                $x = $aes_crypt->Encrypt($score);
                var_dump($x);
                $y = $aes_crypt->decrypt($x);
                var_dump($y);
exit;
                var_dump($this->prihlaska, $this->dotazsql);exit;
                */

                // přepsat info z přihlášky do tabulky c_m_otestovan
                echo "<!-- $this->dotazsql -->";
                if ($db2->Query($this->dotazsql)) {
                    if ($this->prihlaska['id_z'] > 0) {
                        $this->dbsource->Query("
                    UPDATE {$this->databasename}.www_form_1
                        SET stav=1
                        WHERE id_z = {$this->prihlaska['id_z']}");
                    }
                    echo "Zápis proběhl v pořádku.";
                }
                else {
                    echo "Během zápisu došlo k chybě.";
                    $this->prihlaska['err'] = "new";
                }
            }
            else {
                echo "<font color=red><strong>" . $this->prihlaska['err'] . "</strong></font>";
            }
            return $this->prihlaska;
        }
        else if (isset($vyber_akci) && $vyber_akci == 'Vrátit') {
            $this->prihlaska = array();
            $this->prihlaska['err'] = '';
            $this->prihlaska['id_z'] = $this->inglob->getInteger('id_z', 0);  // id_z je id přihlášky na akci
            $this->prihlaska['vzkaz'] = $this->inglob->getString('vzkaz', '');  // vzkaz je vzkaz od managera testování
            $this->prihlaska['mobil'] = $this->inglob->getString('mobil', '');  // mobil je id přihlášky na akci
           // if (mb_strlen($this->prihlaska['vzkaz']) > 1) {
                // vzkaz od admina je všude mapován do databáza za mobilní číslo
            //    $this->prihlaska['mobil'] = $this->prihlaska['mobil'] . "|" . $this->prihlaska['vzkaz'];
           // }
            $this->dotazsql = "UPDATE {$SECURE_DATABASE_NAME}.c_m_otestovan
                        SET mobil='" . addslashes($this->prihlaska['mobil']) . "', vzkaz='".addslashes($this->prihlaska['vzkaz'])."' WHERE id_z=" . $this->prihlaska['id_z'];
            $this->dbsource->Query($this->dotazsql);

            $this->VratitTestujicimuChyba($db2, $this->prihlaska['id_z']);
        }
    }

    /**
     * @param int $mitch_odpovedi
     * @return bool
     */
    protected function is_mitch_odpovedi_ok($mitch_odpovedi)
    {
        return (bool) preg_match("/^([0-9]|[1-3][0-9]|4[0-4])$/", $mitch_odpovedi);
    }

    /**
     * @param string|int $mitch_iq
     * @return bool
     */
    protected function is_mitch_iq_ok($mitch_iq)
    {
        return preg_match("/^(0|[8-9][0-9]|1[0-3][0-9]|14[0-5])$/", $mitch_iq)
            || preg_match("/^(pod 8[0-3])$/", $mitch_iq)
            || preg_match("/^(více než 1[3-4][2-6])$/", $mitch_iq)
        ;
    }

    /**
     * @param mixed $raven_percentile
     * @return bool
     */
    protected function check_detska_mensa_percentile_8_13($raven_percentile)
    {
        $is_percentile_zero = mb_strlen($raven_percentile) == 0;
        $is_percentile_outta_range = ((intval($raven_percentile) < 11 || intval($raven_percentile) > 100) && trim($raven_percentile) != "pod 11");

        return $is_percentile_zero || $is_percentile_outta_range;
    }

    public function VratitTestujicimuChyba($db2, $id_z) {
        global $SECURE_DATABASE_NAME, $PUBLIC_DATABASE_NAME;

        $this->id_z = $id_z;
        $this->otestovany = null;

        if (access("class", $this->men, $this->user["id_m"], $this->dbsource)) {
            if ($db2->Query("UPDATE {$PUBLIC_DATABASE_NAME}.www_form_1 SET stav=2 WHERE  id_z={$this->id_z}")) {

                if ($db2->Query("UPDATE {$SECURE_DATABASE_NAME}.c_m_otestovan SET stav=-1 WHERE  id_z={$this->id_z}")) {
                    echo "<p><font color='red'>Záznam byl nastaven do stavu <em>vráceno testujícímu</em>.</font></p>
                        <p><a href='./index.php?men=men19.2.1.0'>Zpět na výpis.</a></p>";

                    $this->tmp = $db2->Query("
                        SELECT id_testujiciho, c_m_otestovan.jmeno, c_m_otestovan.prijmeni, m_members.email, m_members.jmeno as tjmeno, m_members.prijmeni as tprijmeni, c_m_otestovan.mobil
                        FROM {$SECURE_DATABASE_NAME}.c_m_otestovan, {$PUBLIC_DATABASE_NAME}.m_members
                            WHERE c_m_otestovan.id_z={$this->id_z} AND m_members.id_m=c_m_otestovan.id_testujiciho
                        ");
                    $this->vysledek = $db2->FetchArray($this->tmp);

                    $this->tmp1 = $db2->Query("SELECT mobil, jmeno, prijmeni, vzkaz FROM {$SECURE_DATABASE_NAME}.c_m_otestovan WHERE id_z=" . $this->id_z);
                    $this->otestovany = $db2->FetchArray($this->tmp1);

                    $mailbody = "
                        <p>
                        Přihláška otestovaného {$this->otestovany['jmeno']} {$this->otestovany['prijmeni']} Vám byla vrácena zpět k ověření zadaných dat administrátorem testování.
                        Vzkaz od administrátora testování: {$this->otestovany['vzkaz']}.
                        </p>
                    ";

                    $is_sent = MailerFactory::getMailer()->sendDefaultMail(
                        MailerInterface::NOREPLY_MAIL_ADDRESS,
                        "Intranet Mensy - Testování",
                        [
                            "{$this->vysledek['tjmeno']} {$this->vysledek['tprijmeni']}" => $this->vysledek['email'],
                        ],
                        SUBJECT_PREFIX . " Vrácení přihlášky otestovaného ke kontrole",
                        $mailbody
                    );

                    if ($is_sent) {
                        echo "<p>Testující byl informován e-mailem.</p>";
                    }
                    else {
                        echo "<p>Email testujícímu s informací <em>nebyl</em> odeslán.</p>";
                    }
                }
            }
        }
    }

    /**
     * @param database $db2
     * @param string $SECURE_DATABASE_NAME
     * @param string $PUBLIC_DATABASE_NAME
     * @param AES $aes_crypt
     * @return array
     */
    public function GetNoveOtestovane($db2, $SECURE_DATABASE_NAME, $PUBLIC_DATABASE_NAME, $aes_crypt)
    {
        $query = "
        SELECT
            x.*,
            CASE
                WHEN (is_adult AND email_verified = 1) THEN 1
                WHEN (NOT is_adult AND email_zz_verified = 1) THEN 1
                ELSE 0
            END has_verified_email
        FROM (
            SELECT
                ot.id, ot.typ, ot.titul, ot.jmeno, ot.prijmeni, ot.titul_za_jmenem, ot.pohlavi, ot.datum_testu, ot.datum_narozeni,
                ot.byt, ot.ulice, ot.mesto, ot.psc, ot.byt1, ot.ulice1, ot.mesto1, ot.psc1, ot.mobil, ot.telefon,
                ot.id_testujiciho, ot.iq, ot.per, ot.certifikat, ot.pocet_certifikatu, ot.mitch_odpovedi, ot.mitch_iq, ot.id_z, ot.jmeno_zz,
                DATE_FORMAT(ot.datum_testu, '%e.%c.%Y') datum_testu2,
                DATE_FORMAT(ot.datum_narozeni, '%e.%c.%Y') datum_narozeni2,
                m.jmeno jmeno2, m.prijmeni prijmeni2, 
                ot.email,  ot.email_status, 
                ot.email2, ot.email2_status,
                IF(ot.email  IS NOT NULL AND ot.email_status  AND ot.email_status  IS NOT NULL, 1, 0) email_verified,
                IF(ot.email2 IS NOT NULL AND ot.email2_status AND ot.email2_status IS NOT NULL, 1, 0) email_zz_verified,
                IF(wf.vek >= 15, 1, 0) is_adult
            FROM {$SECURE_DATABASE_NAME}.c_m_otestovan AS ot
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.m_members AS m ON ot.id_testujiciho = m.id_m
                LEFT JOIN {$PUBLIC_DATABASE_NAME}.www_form_1 wf ON wf.id_z = ot.id_z
            WHERE ot.stav = 0
        ) x
        ORDER BY x.datum_testu ASC, x.id_testujiciho ASC, FIELD(has_verified_email, 1, 0)
        ";

        $this->tmp = $db2->Query($query);

        if ($db2->getNumRows($this->tmp) == 0) {
            return array();
        }

        while ($row = $db2->FetchArray($this->tmp)) {
            $this->vysledek['select'][] = $row;
        }

        return $this->vysledek;
    }

    /**
     * Funkce vyřadí otestovaného ze seznamu bez zápisu do databáze členů
     * Pokud je funkce vyřazení - pak vrací 1 a provede ji
     * Pokud není funkce vyřazení vrací 0
     *
     * @global type $SECURE_DATABASE_NAME
     * @param type $db2
     * @param type $securedtbname
     * @return int
     */
    public function VyraditBezZapisu($db2, $securedtbname) {
        global $SECURE_DATABASE_NAME;

        $this->id_vyrazovaneho = $this->inglob->getInteger("vyradit");
        if (!is_null($this->id_vyrazovaneho) && $this->id_vyrazovaneho > 0) {
            $this->dotazsql = "UPDATE {$SECURE_DATABASE_NAME}.c_m_otestovan SET stav = 2 WHERE id={$this->id_vyrazovaneho} ";
            $db2->Query($this->dotazsql);
            return 1;
        }
        return 0;
    }

    public function DejClenaProZtotozneni($db2, $securedtbname) {
        global $SECURE_DATABASE_NAME;

        $id = $this->inglob->getInteger('id', 0);

        $this->tmp = $db2->Query("
        SELECT
            *,
            DATE_FORMAT(datum_testu, '%e.%c.%Y') AS datum_testu2,
            DATE_FORMAT(datum_narozeni, '%e.%c.%Y') AS datum_narozeni2,
            DATE_FORMAT(datum_narozeni, '%Y-%c-%e') AS datum_narozeni3
        FROM
            {$SECURE_DATABASE_NAME}.c_m_otestovan
        WHERE id={$id}");

        if ($db2->getNumRows($this->tmp) == 0) {
            return NULL;
        }
        else {
            return $db2->FetchAssoc($this->tmp);
        }
    }

    public function DejPodobneProZtotozneni($db2, $securedtbname, $row) {
        global $SECURE_DATABASE_NAME;

        $WHERE = "";
        if ($this->inglob->jeGLobals('jmeno')) {
            $WHERE .= " AND jmeno LIKE '%" . $row["jmeno"] . "%'";
        }
        if ($this->inglob->jeGLobals('prijmeni')) {
            $WHERE .= " AND ( prijmeni LIKE '%" . $row["prijmeni"] . "%' OR rodne_prijmeni LIKE '%" . $row["prijmeni"] . "%' )";
        }
        if ($this->inglob->jeGLobals('datum_narozeni')) {
            $WHERE .= " AND datumnar = '" . $row["datum_narozeni3"] . "'";
        }
        if ($this->inglob->jeGLobals('psc')) {
            $WHERE .= " AND psc LIKE '%" . $row["psc"] . "%'";
        }

        $this->dotazsql = "
        SELECT
            *,
            DATE_FORMAT(datumnar, '%e.%c.%Y') AS datumnar2,
            DATE_FORMAT(datumtestu1, '%e.%c.%Y') AS datumtestu12
        FROM
            {$SECURE_DATABASE_NAME}.c_m_members
        WHERE
            1 " . $WHERE . " LIMIT 20";


        $this->tmp = $db2->Query($this->dotazsql);

        if ($db2->getNumRows($this->tmp) == 0) {
            return NULL;
        }
        else {
            $this->vysledek = array();
            while ($this->vysledek[] = $db2->FetchAssoc($this->tmp)) {

            }
        }
    }

    // notices temporary fixed with shut-up @ operator
    public function dejVek($narozen, $dattestu)
    {
        $rozkladnarozeni = explode('.', $narozen);
        $rozkladtest = explode('.', $dattestu);
        $diference = mktime(0, 0, 0, @intval($rozkladtest[1]), @intval($rozkladtest[0]), @intval($rozkladtest[2])) - mktime(0, 0, 0, @intval($rozkladnarozeni[1]), @intval($rozkladnarozeni[0]), @intval($rozkladnarozeni[2]));

        return (int) ($diference / (60 * 60 * 24 * 365));
    }

    protected function standardPercentily($percentil, $datumNar, $datum_test) {
        if ($this->dejVek($datumNar, $datum_test) < 8) {
            // ošetření chyb starých zápisů
            if ($percentil <= 25) {
                $percentil = "25 a méně";
            }
            elseif ($percentil > 25 && $percentil < 76) {
                $percentil = "26-75";
            }
            elseif ($percentil > 75 && $percentil < 91) {
                $percentil = "76-90";
            }
            elseif ($percentil > 90 && $percentil < 99) {
                $percentil = "91-98";
            }
            elseif ($percentil > 98) {
                $percentil = "99-100";
            }
            switch (trim($percentil)) {
                case '<25':
                    $percentil = "25 a méně";
                    break;
                case '30-70':
                    $percentil = "26-75";
                    break;
                case '70-90':
                    $percentil = "76-90";
                    break;
                default:
                    break;
            }
        }
        else {
            if($percentil <=25 || trim($percentil)=="pod 11" ){
                $percentil="25 a méně";
            }
        }
        if (trim($percentil) == "25 a méně" || intval($percentil) <= 25) {
            $slovo = "Podprůměr";
        }
        if (trim($percentil) == "26-75" || (intval($percentil) >= 26 && intval($percentil) <= 75)) {
            $slovo = "Průměr";
        }
        if (trim($percentil) == "76-90" || (intval($percentil) >= 76 && intval($percentil) <= 90)) {
            $slovo = "Mírný nadprůměr";
        }
        if (trim($percentil) == "91-98" || (intval($percentil) >= 91 && intval($percentil) <= 98)) {
            $slovo = "Výrazný nadprůměr";
        }
        if (trim($percentil) == "99-100" || (intval($percentil) >= 99 && intval($percentil) <= 100)) {
            $slovo = "Vynikající intelekt";
        }
        return array('per' => $percentil, 'slovo_perc' => $slovo);
    }
}
