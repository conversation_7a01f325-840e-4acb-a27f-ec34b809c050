<?PHP
require_once("../ssl_library_new/inquiry_sel.l");				// vypis anket
require_once("../ssl_library_new/to_date.l");					// prace s datem
require_once("../ssl_library_new/get_user_id.l");				// udaje uzivatele

if ($men=="men2.1.1.0") {
	$a_user=user($login,$heslo,0,$db);
	$user_id=$a_user["id_m"];
	$private=1;
} else $private=0;

?>

<p><b><u>Výpis všech <?php echo $private?"vašich ":""?>anket v systému.</u></b><br><br>
<?PHP
if ($private) $a_inq = inquiry_sel($db, $user_id, 2); else $a_inq = inquiry_sel($db, @$blank, 3);		// vypis vsech anket
$i = count($a_inq);
	if($i==0){
		echo $private?"Nemáte <PERSON> ankety.":"V systému není <PERSON> anketa.";
	}
	while($i--){
		$a_user = get_user_id($db, $a_inq[$i]["owner"]);	// udaje uzivatele
		?>
		<table width="90%" border="0" cellspacing="0" cellpadding="1" bgcolor="#f3f3f3">
		<tr bgcolor="#e1e2dc">
			<td width="80"><p><b>Název:</b></td>
			<td><p><?PHP echo $a_inq[$i]["name"]?></td>
			<td align="right"><p><a href="index.php?men=<?PHP echo $men?>&id_ank=<?PHP echo $a_inq[$i]["id_ank"]?>&s_content=detail_vote.i"><img src="images/graf.gif" width="15" height="15" border="0" alt="Detail"></a>
				&nbsp &nbsp &nbsp
        <a href="index.php?men=<?PHP echo $men?>&id_ank=<?PHP echo $a_inq[$i]["id_ank"]?>&s_content=delete_vote.i"><img src="images/_delete.gif" width="15" height="15" border="0" alt="Smazat"></a></td>
		</tr>
		<tr>
			<td width="80"><p><b>Popis:</b></td>
			<td colspan="2"><p><?PHP echo $a_inq[$i]["popis"]?></td>
		</tr>
		<tr>
			<td width="80"><p><b>Založil:</b></td>
			<td colspan="2"><p><?PHP echo $a_user["jmeno"]." ".$a_user["prijmeni"]?></td>
		</tr>
		<tr>
			<td width="80"><p><b>Začátek:</b></td>
			<td colspan="2"><p><?PHP echo to_date($a_inq[$i]["start"])?></td>
		</tr>
		<tr>
			<td width="80"><p><b>Konec:</b></td>
			<td colspan="2"><p><?PHP echo to_date($a_inq[$i]["end"])?></td>
		</tr>
		<tr>
			<td colspan="3"><hr size="1"></td>
		</tr>
		</table>
	<?PHP
	}
?>