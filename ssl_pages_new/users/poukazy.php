<?php
/*
Výroční poukazy na test 2016
(c) 2016 <PERSON><PERSON><PERSON><PERSON>, v<PERSON><EMAIL>

Zmenovnik
2016-01-31 Puvodni
2016-02-04 <PERSON>, martin.sedl<PERSON><EMAIL>
2016-02-06 TK: downgrade na verzi PHP na mensa serveru, reformat, aby se kratke vetve rozhodovaciho stromu resily hned a skript se ukoncil
2022-01-04 TK: rok poukazu je automaticky
2025-03-12 MD: Nový Sendgrid Mailer
*/

use \mailer\Mailer;

// Nastaveni

// Stranka je zatim naprogramovana, ze umi pracovat jen s jednim typem poukazu naráz,
// v soucasne dobe to musí být integer, kde cislo odpovida roku, na ktery je poukaz platny.
$now = new DateTime("now");
$ROK_POUKAZU = (int) $now->format("Y");

// Navazana data
$DATUM_PLATBY = "{$ROK_POUKAZU}-03-01";       // datum, do kdy ma probehnout platba - jako string
$DATUM_REGISTRACE = "{$ROK_POUKAZU}-04-01";   // datum do kdy je treba poukaz registrovat - jako string
$DATUM_PLATNOSTI = "{$ROK_POUKAZU}-12-31";    // datum do kdy je treba poukaz uplatnit - termin testu - jako string



function mime_header_encode($text, $encoding = "utf-8") {
    return "=?$encoding?Q?" . quoted_printable_encode($text) . "?=";
}


function posli_email($email, $voucherData, $code)
{
    global $a_user;
    $predmet = "Poukaz na testování IQ zdarma";
    $header = "From: ".mime_header_encode("Mensa Česko", "utf-8")." <<EMAIL>>
Return-Path: <EMAIL>
Content-Type: text/plain;charset=\"utf-8\"
Content-Transfer-Encoding: 8bit
MIME-Version: 1.0
X-Mailer: PHP/Mensaweb
";

    $body = "Vážená paní, vážený pane,

s radostí Vám oznamujeme, že Vám náš člen/naše členka $a_user[jmeno] $a_user[prijmeni] daroval poukaz na bezplatné testování IQ.

Rádi bychom Vás proto pozvali na stránky https://mensa.cz/, kde si můžete z naší široké nabídky vybrat nejvhodnější místo a termín.

Při vyplnění přihlášky nezapomeňte zadat slevový kód $code. Tento kód je nepřenosný, platí do $voucherData[prihlasit_do] a jste oprávněn jej využít pouze Vy - $voucherData[jmeno] $voucherData[prijmeni].

Společnost Mensa provádí testování IQ prostřednictvím mezinárodně uznávaného standardizovaného testu schváleného mezinárodním dozorčím psychologem společnosti. Nad správností prováděných testování dohlíží i psycholog Mensy Česko.

Samotný test dospělých od 14 let trvá 40 minut, dětí starších 9 let 30 minut, 
mladších dětí 20 minut čistého času, avšak celá procedura včetně instruktáže 
a dalších úkonů zabere minimálně jednu hodinu. 

Každý, kdo se zúčastní mensovního testu, dostane potvrzení o absolvování s výsledkem. Toto potvrzení má mezinárodní platnost.

V případě jakýchkoliv dotazů se, prosím, obraťte na naši sekretářku na adrese: <EMAIL>.

Budeme se na Vás těšit,
s přáním dobrého výsledku
Vaše Mensa Česko

";

    // odesle mail a vrati hodnotu funkce, ktera mail odesila
    // bool mail ( string $to , string $subject , string $message [, string $additional_headers [, string $additional_parameters ]] )
    // Returns TRUE if the mail was successfully accepted for delivery, FALSE otherwise.
    $mailer = new Mailer();
    return $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $email, mime_header_encode($predmet, "utf-8"), $body, $header, "-r <EMAIL>");
}



// dotaz na to jestli ma clen narok na poukaz a jestli jej jiz nevygeneroval
// je treba omezit jen na poukazy pro tento rok
$selectSql = sprintf(
    "SELECT *
     FROM m_members_maji_zaplaceno AS z
        LEFT JOIN m_poukazy_na_test AS p ON (p.id_m = z.id_m AND p.typ = {$ROK_POUKAZU})
     WHERE z.id_m = %d AND z.platba_tento_rok = 'ANO' AND z.datum_platby < '{$DATUM_PLATBY}'",
    $a_user['id_m']
);
$result = $db->Query($selectSql);
$row = $db->FetchAssoc($result);
print($row);




// Uživatel nemá nárok na poukaz (dotaz nevrátil platný záznam)
if ($row == FALSE) {
    echo "<p>Poukaz na testování zdarma je dostupný jako odměna za včasné zaplacení členského příspěvku na rok {$ROK_POUKAZU} pro ty členy,
		kteří zaplatili v řádném termínu, tj. platba dorazila Mense do konce února.
		 Zaplaťte příspěvky na stránce <a href='index.php?men=men1.8.0.0'>platby</a>.</p>";
    return;
}




// Uživatel už poukaz vygeneroval
// zobraz poukaz, ktery ma
if (@$row['kod'] !== NULL) {

    $voucherData = array(
        'jmeno' => $row['jmeno_obdarovaneho'],
        'prijmeni' => $row['prijmeni_obdarovaneho'],
        'kod' => $row['kod'],
        'zmena' => $row['zmena'],
        'prihlasit_do' => $row['prihlasit_do'],
    );
    require __DIR__ . '/poukazy-code.php';
    return;
}




// Uživatel má nárok (dotaz vrátil platný záznam bez vyplněného pole 'kod')
// 2016-02-06, TK: na nasi verzi php nefunguje slouceni poli pres +
$formData = array_merge(array('jmeno' => NULL, 'prijmeni' => NULL, 'email' => NULL), $_POST);
$isFormSubmitted = $_SERVER['REQUEST_METHOD'] === 'POST';

// formular s poukazem byl odeslan, zpracuj ho
if ($isFormSubmitted && !empty($formData['jmeno']) && !empty($formData['prijmeni']) && !empty($formData['email'])) {

    // prirpav na vlozeni kodu
    $updateSql = sprintf(
        "INSERT INTO m_poukazy_na_test
            (typ, id_m, jmeno_obdarovaneho, prijmeni_obdarovaneho, email_pro_zaslani_kodu, aktivovat_do, prihlasit_do)
         VALUES (%d, %d, '%s', '%s', '%s', '%s', '%s')",
        $ROK_POUKAZU,   // typ kuponu, zatim nepouzito
        $a_user['id_m'],
        // 2016-02-06, TK: musi se pouzit addslashes, protoze mysql_real_escape_string nefunguje jelikoz mame zmrsene kodovani
        addslashes($formData['jmeno']),
        addslashes($formData['prijmeni']),
        addslashes($formData['email']),
        $DATUM_REGISTRACE,
        $DATUM_PLATNOSTI
    );


    // proved vlozeni kodu
    $result = $db->Query($updateSql);
    if ($result === true) {
        $selectSql = sprintf("SELECT * FROM m_poukazy_na_test WHERE typ = %d AND id_m = %d ", $ROK_POUKAZU, $a_user['id_m']);
        $result = $db->Query($selectSql);
        $row = $db->FetchAssoc($result);
        $code = $row['kod'];

        // zkonvertuj
        $voucherData = array(
            'jmeno' => $formData['jmeno'],
            'prijmeni' => $formData['prijmeni'],
            'kod' => $code,
            'zmena' => date('Y-m-d'), // cas aktualni zmeny
            'prihlasit_do' => $row['prihlasit_do']
        );

        // odesli e-mail
        $res = posli_email($formData['email'], $voucherData, $code);
        if ($res) {
            echo "<p>Kód byl odeslán e-mailem na adresu $formData[email].</p>";
        }


        require __DIR__ . '/poukazy-code.php';


    } else {
        echo "<p>Z nějakého důvodu se nepodařilo údaje uložit, zkuste to později.</p>";
    }


// zobraz formular pro zadani informaci o kodu, pokud jeste neuběhla lhůta
} else {
    if (new DateTime() <= new DateTime($DATUM_REGISTRACE))
    {
        require __DIR__ . '/poukazy-form.php';
    } else {
        // datum je treba prepocit a naformatovat
        $date = new DateTime($DATUM_REGISTRACE);
        $date->sub(new DateInterval('P1D'));
        $a = $date->format('d. m. Y');
        echo"<p>Průkaz bylo třeba registrovat do {$a} včetně.</p>";
    }
}
