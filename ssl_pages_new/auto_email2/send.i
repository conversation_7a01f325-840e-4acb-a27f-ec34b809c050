<h1><PERSON><PERSON><PERSON> zpru</h1>
<!-- <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->

<?php
/*
	Rozesílání zpráv
	<PERSON>:
        2013-03-01, TK: Zpráv může rozeslat člen maximálně 5 za den.
*/

function get_auth_konfs(){
  global $a_user, $db, $konference, $auth_konfs;
  if (isset($auth_konfs)) return $auth_konfs; //cached result
	$SQL = "SELECT ae.id_konf FROM m_ae_konfcfg ae WHERE ae.id_m={$a_user['id_m']} AND ae.banned=0";
  $q_konf_loggedin = $db->query($SQL);
  $konf_in = array();
  while ($k = $db->FetchRow($q_konf_loggedin)){
    list($konf_in[]) = $k;
  }
  $auth_konfs = array();
  foreach ($konference as $id=>$konf){
    if (isset($konf['auth_send'])){
      //pouze pro vybrane
      $can_send = (is_numeric($konf['auth_send']) && $a_user['id_m'] == $konf['auth_send'])
        || (is_array($konf['auth_send']) && in_array($a_user['id_m'], $konf['auth_send']));
    } else {
      //pro prihlasene do konference

      $can_send = (($konf['type'] == KONF_MEN) != in_array($id, $konf_in));
    }
    if ($can_send) {
      $auth_konfs[$id] = $konf;
    }
  }
  return $auth_konfs;
}




require 'config.i';
require 'konference.config.i';




if (isset($int_ex[$a_user['id_m']])){
  $SEND_INTERVAL = $int_ex[$a_user['id_m']];
}



function auth_send($typ){
  global $send_auth, $send_disable, $typy, $a_user;
  //$typ: array( 0=> typ zpravy (oz|ko), 1=> podtyp = id obecne zpravy/konference)
  switch ($typ[0]){
    case 'oz':
      if ($send_disable) return false;
      $id_m = (int)$a_user['id_m'];
      if (isset($send_auth[$typ[1]][$id_m])) return (bool)$send_auth[$typ[1]][$id_m]; //individual setting
      if (isset($send_auth[0][$id_m])) {
        return (bool)$send_auth[0][$id_m]; //master setting
      }
      return (bool)$typy[$typ[1]]['default_auth']; //default type setting
    case 'ko':
      $auth_konfs = get_auth_konfs();
      return (isset($auth_konfs[$typ[1]]));
  }
}







/////\\\\\
// BODY \\
//\\\///\\




//nacteme z DB seznam kraju a typu zprav
$q_kraje = $db->query("SELECT id_kraj, kraj_name FROM m_list_kraj WHERE kraj_name NOT LIKE '---%' ORDER BY id_kraj"); //podminkou "not like" se vyradi kraj "----neuveden----"
$kraje = $db->FetchAssocKey($q_kraje, 'id_kraj');
$q_typy = $db->query("SELECT id_type, type_name, default_send, default_auth FROM m_ae_types ORDER BY id_type");
$typy = $db->FetchAssocKey($q_typy, 'id_type');

//overeni moznosti odeslani v intervalu X dni
$q_last_time = $db->query("SELECT UNIX_TIMESTAMP(cas) FROM m_ae_mails WHERE id_m='{$a_user['id_m']}' AND konference IS NULL ORDER BY cas DESC LIMIT 1");
if ($db->getNumRows($q_last_time) == 0){
  $send_disable = false;
} else {
  list($last_time) = $db->FetchRow($q_last_time);
  if ($last_time > strtotime('-'.$SEND_INTERVAL.' days')){
    $send_disable = $last_time + 86400*$SEND_INTERVAL;
  } else {
    $send_disable = false;
  }
}


if (isset($_POST['body'])){
  try {
    //shromazdime data z formulare
    $err = array();
    if (!empty($_POST['typ'])) {
      $s_typ = $_POST['typ'];
      $s_typ_data = explode('_', $s_typ);
      if (count($s_typ_data) != 2){
        $err[] = "Chybný typ zprávy!";
      } else {
        $s_typ_data[1] = (int)$s_typ_data[1];
      }
    } else {
      $s_typ = null;
      $err[] = "Musíte vybrat typ zprávy!";
    }
    if ($s_typ !== null && !auth_send($s_typ_data)) {
       $err[] = "Nemáte práva pro zaslání tohoto typu zprávy!";
    }
    if ($s_typ_data[0] == 'oz') //pouze u obecnych zprav pouzivame kraje/skupiny
    {
      if (!empty($_POST['kraj'])) {
        $s_kraj = array_map('intval', $_POST['kraj']);
      } else {
        $s_kraj = array();
        $err[] = "Musí být vybrán alespoň jeden kraj!";
      }
      if (!empty($_POST['uziv'])) {
        $s_uziv = array_map('intval', $_POST['uziv']);
      } else {
        $s_uziv = array();
        $err[] = "Musíte vybrat alespoň jeden okruh uživatelů!";
      }
    }
    if (!empty($_POST['subject'])) {
      $s_subject = $_POST['subject'];
      if (strlen($s_subject) > 100){
        $err[] = "Předmět může být dlouhý maximálně 100 znaků!";
      }
    } else {
      $err[] = "Musíte zadat předmět zprávy!";
    }
    if (!empty($_POST['body'])) {
      $s_body = $_POST['body'];
    } else {
      $err[] = "Musíte vyplnit tělo zprávy!";
    }
    if ($s_typ_data[0] == 'oz'){
      if ($send_disable){
        $err[] = "Nemůžete nyní zasílat obecné zprávy!";
      }
    }
    //end of input validation
    if (!empty($err)){
      throw new Exception(implode('<br>', $err));
    }

    if ($s_typ_data[0] == 'oz'){

      //odeslani obecne zpravy
      $send_body = $s_body;
      $h_uziv = 0;
      //target user groups and kraje are logged as bits, so kraje with numbers 1, 3, 6 would be logged as 00100101
      foreach ($s_uziv as $u){
        $h_uziv += pow(2, $u);
      }
      $h_kraje = 0;
      foreach ($s_kraj as $k){
        $h_kraje += pow(2, $k);
      }
      $db->query("INSERT INTO m_ae_mails (id_m, cas, typ, pro_uziv, kraje, subject, text_body, uid)
        VALUES({$a_user['id_m']}, NOW(), {$s_typ_data[1]}, $h_uziv, $h_kraje, '".$db->getEscapedString($s_subject)."', '".$db->getEscapedString($send_body)."', '".$db->getEscapedString(@$_POST['uid'])."')");
      if ($SEND_INTERVAL > 0){
        $send_disable = strtotime('+'.$SEND_INTERVAL.' days');
      }
    } else {
      //odeslani konferencni zpravy
      $db->query("INSERT INTO m_ae_mails (id_m, cas, konference, subject, text_body, uid)
        VALUES({$a_user['id_m']}, NOW(), {$s_typ_data[1]}, '".$db->getEscapedString($s_subject)."', '".$db->getEscapedString($s_body)."', '".$db->getEscapedString(@$_POST['uid'])."')");
    }
    //reset form
    unset($s_kraj);
    unset($s_uziv);
    unset($s_typ);
    unset($s_subject);
    unset($s_body);

    $notice = "E-mail byl úspěšně odeslán.";
  } catch (Exception $ex){
    $input_error = $ex->getMessage();
  }
}
?>


<ul style="color: red;">
    <li style="font-size: 125%; font-weight: bold;">Nezasílejte zprávy místních skupin nebo SIGů do obecných zpráv!</li>
    <li style="font-size: 125%; font-weight: bold;">Zvolte kraj, ve kterém se akce KONÁ!</li>
    <li style="font-size: 125%; font-weight: bold;">Rozesílání slouží výhradně k oznamování MENSOVNÍCH aktivit,
        vyjma kategorie komerční zprávy, nabídky práce a nemensovní nekomerční zprávy.</li>
    <li style="font-size: 125%; font-weight: bold;">Pokud si nejste jisti, prosím, zeptejte se nejprve na <a
                href="mailto:<EMAIL>?subject=Klasifikace%20zprávy"><EMAIL></a>.
    </li>
</ul>

<p style="color:red">Jakékoliv využití pro osobní, nebo dokonce komerční účely (mimo Komerčních zpráv a Nemensovních nekomerčních zpráv) je striktně zakázáno a bude bez varování trestáno dlouhodobým odebráním možnosti rozesílat zprávy. Zásadní porušení (především zasílání zpráv urážlivých či jinak nevhodných) nebo zasílání obecných zpráv do krajů, kde se nekonají, budou trestána úplným vyloučením z intranetu. Podrobnější vysvětlení naleznete níže. Za den může uživatel zaslat maximálně 7 zpráv.</p>



<script type="text/javascript">
function dedswitch(anch){
  el = document.getElementById('dedicat');
  if (el.style.display=='none'){
    el.style.display = 'block';
    anch.innerHTML = '(-)';
  } else {
    el.style.display = 'none';
    anch.innerHTML = '(+)';
  }
}
function select_type(select){
  if (select.options[select.selectedIndex].parentNode.id &&  select.options[select.selectedIndex].parentNode.id == 'obxzpr'){
    document.getElementById('sel_caption').style.display = 'block';
    document.getElementById('sel_kraj').style.display = 'block';
    document.getElementById('sel_uziv').style.display = 'block';
  } else {
    document.getElementById('sel_caption').style.display = 'none';
    document.getElementById('sel_kraj').style.display = 'none';
    document.getElementById('sel_uziv').style.display = 'none';
  }
}
</script>
<?php
if (isset($input_error)){
  ?><p class="error"><?php echo $input_error; ?></p><?php
}
if (isset($notice)){
  ?><p class="notice"><?php echo $notice; ?></p><?php
}
if (empty($a_user['email']) || trim($a_user['email']) == ''){ //ma uzivatel nastaveny mail?
?>
<p class="error">Nemáte nastavený e-mail!</p>
<p>Abyste mohli rozesílat zprávy, musíte jej nejprve ve Vašem uživatelském účtu nastavit.</p>
<?php
} else {
if ($send_disable) {
  echo '<p>Další obecnou zprávu můžete odeslat <b>'.date('j.n.Y G:i', $send_disable).'</b></p>';
}



////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// kontrola, zda odeslaných zpráv není příliš
$q_pocet_odeslanych = $db->query("SELECT count(*) FROM m_ae_mails WHERE cas > DATE_SUB(NOW(), INTERVAL 1 DAY) and id_m = {$a_user['id_m']}");
$r_pocet_odeslanych = $db->FetchArray($q_pocet_odeslanych);
if ($r_pocet_odeslanych[0] >= 7)
{
 echo '<p>Za den nemůžete zaslat více než 7 zpráv.</p>';
 return;
}

?>






<script type="text/javascript">
  var jedenKrajAlerted = false;
  function jedenKraj()
  {
    // tohle odkomentovat pokud se uzivatel ma varovat jen jednou
    // if (jedenKrajAlerted) return true;
    var one = false;
    var inputs = document.getElementById('sel_kraj').getElementsByTagName('input');
    for (var i = 0; i < inputs.length; i++)
    {
      if (inputs[i].checked)
      {
        if (one)
        {
          jedenKrajAlerted = true;
          alert('Vybíráte více než jeden kraj! Vyberte kraj, ve kterém SE AKCE KONÁ!');
          return true;
        } else {
          one = true;
        }
      }
    }
  }
</script>
<form method="post" action="" onsubmit="return confirm('Máte poslední možnost ujistit se, že zpráva je taková, jakou jí chcete mít, po kliknutí na OK jí rozešleme a již nepůjde vrátit.')">
<?php
//vypsat chyby



//vyber kraje
$s_kraje = '';
$i = 0;
$cols = 1;
foreach ($kraje as $kraj){
  $checked = (!isset($s_kraj) || in_array($kraj['id_kraj'], $s_kraj)) ? ' ' : '';
  $s_kraje .= '<input type="checkbox"'.$checked.' name="kraj[]" onclick="jedenKraj()" value="'.$kraj['id_kraj'].'">&nbsp;'.htmlspecialchars($kraj['kraj_name']).'<br>';
  if (++$i % 4 == 0){
    $s_kraje .= '</span></td><td style="vertical-align:top"><span style="white-space:nowrap">';
    $cols++;
  }
}

?>



<p>Váš e-mail pro zpětnou komunikaci je: <?php echo '<a href="mailto:'.htmlspecialchars($a_user['email']).'">'.htmlspecialchars($a_user['email']).'</a>'; ?></p>
<table id="dedicat">
  <tr>
    <th style="width:80px">Typ:</th>
    <td colspan="<?php echo $cols; ?>" style="vertical-align:top">
      <select name="typ" onchange="select_type(this)" id="type_sel">
        <option style="color:#999" value="">--Vyberte--</option>





<?php
$auth_konfs = get_auth_konfs();
if (!empty($auth_konfs)){
?>
        <optgroup label="Konference MS">
        <?php
          foreach ($auth_konfs as $id=>$konf){
    		  	if($konf["type"]==KONF_MS){
    	            $selected = (isset($s_typ) && $s_typ == 'ko-'.$id) ? ' selected="selected"' : '';
        	        echo "<option value=\"ko_$id\"$selected>".htmlspecialchars($konf['name']).'</option>';
      			}
          }
        ?>
        </optgroup>
        <optgroup label="Konference SIG">
        <?php
          foreach ($auth_konfs as $id=>$konf){
    		  	if($konf["type"]==KONF_SIG){
    	            $selected = (isset($s_typ) && $s_typ == 'ko-'.$id) ? ' selected="selected"' : '';
        	        echo "<option value=\"ko_$id\"$selected>".htmlspecialchars($konf['name']).'</option>';
    			  }
          }
        ?>
        </optgroup>
<?php } ?>
<?php if (!$send_disable) { ?>
        <optgroup label="Obecné zprávy" id="obxzpr">
<?php
  foreach ($typy as $typ) {
    if (!auth_send(array('oz', $typ['id_type']))) continue;
    $selected = (isset($s_typ) && $s_typ == 'oz_'.$typ['id_type']) ? ' selected="selected"' : '';
    echo '<option value="oz_'.htmlspecialchars($typ['id_type']).'"'.$selected.'>'.htmlspecialchars($typ['type_name']).'</option>';
  }
?>
        </optgroup>
<?php }
      if (!empty($auth_konfs)){ ?>
        <optgroup label="Konference Mensovní">
        <?php
          foreach ($auth_konfs as $id=>$konf){
		  	if($konf["type"]==KONF_MEN){
	            $selected = (isset($s_typ) && $s_typ == 'ko-'.$id) ? ' selected="selected"' : '';
    	        echo "<option value=\"ko_$id\"$selected>".htmlspecialchars($konf['name']).'</option>';
			}
          }
        ?>
        </optgroup>
<?php } ?>
      </select>
    </td>
  </tr>
  <tr id="sel_caption">
    <th colspan="2">&nbsp;</th>
  </tr>
  <tr id="sel_kraj">
    <th style="vertical-align:top">Akce se KONÁ v kraji:</th>
    <td style="vertical-align:top"><span style="white-space:nowrap"><?php echo $s_kraje; ?></span></td>
  </tr>
  <tr id="sel_uziv">
    <th style="vertical-align:top">zaslání uživatelům:</th>
    <td colspan="<?php echo $cols; ?>" style="vertical-align:top">
    <span style="white-space:nowrap">
    <?php
      foreach ($uzivatele as $abbr=>$name){
        $checked = (!isset($s_uziv) || in_array($abbr, $s_uziv)) ? ' checked="checked"' : '';
        echo '<input type="checkbox" name="uziv[]"'.$checked.' value="'.htmlspecialchars($abbr).'">&nbsp;'.htmlspecialchars($name).'<br>';
      }
    ?>
    </span>
    </td>
  </tr>
</table>


<p><b>Předmět</b> (maximálně 100 znaků):<br>
<input type="text" style="width:500px" name="subject" value="<?php echo isset($s_subject) ? htmlspecialchars($s_subject) : ''; ?>"><br>
<span style="font-size:0.7em;color:#0e0e0e">Doporučujeme uvést <u>místo, čas a typ akce</u></span>
</p><p>
<b>Tělo zprávy:</b><br>
<textarea name="body" style="width:500px;height:300px"><?php echo isset($s_body) ? htmlspecialchars($s_body) : ''; ?></textarea>
</p>
<p>
  <input type="hidden" name="uid" value="<?php echo md5(uniqid($a_user['id_m'], true)); ?>">
  <input type="submit" value="Odeslat zprávu" style="font-weight:bold">
</p>
</form>

<p>&nbsp;</p>

<h2>Pravidla</h2>

<h3>Úvod</h3>

<p>
Rada Mensy si je vědoma, že pojmy mensovní akce a apolitičnost Mensy umožňují širší výklad. Domnívá se však, že sdružení, jakým je Mensa, by mělo fungovat především na principech zdravého rozumu, širšího konsenzu a ohleduplnosti spíše než na principu mnohasetstránkových definic. Pokud si nejste jisti, jak Vaši zprávu zařadit, upozorňujeme na možnost předem konzultovat zprávy, které svým charakterem nebo charakterem akce vybočují ze současných zvyklostí, <a href="mailto:<EMAIL>">s členem Rady zodpovědným za intranet</a>. Rada Mensy se domnívá, že práce v Mense by měla být založena především na dobré vůli při snaze dosáhnout společného cíle a citlivé ohleduplnosti a zdrženlivosti v oblastech, kde se názory jednotlivých členů liší.
</p>


<h3>Hlavní myšlenka</h3>
<p>To, že se nějaká aktivita líbí Vám, nezaručuje, že se bude líbit dalším 2&nbsp;500 členům.
Hlavním principem při rozesílání zpráv je proto <b>neobtěžovat</b> a nevnucovat nikomu to,
o co si explicitně nepožádal - i za cenu, že občas může o nějakou zajímavou informaci přijít.
Proto rozesílejte zprávy přesně podle pravidel, a to i když Vám přijde, že zmíněná akce má například přesah regionální
a stáli by o ni mensané i ve vedlejším kraji, nebo pokud máte akci, kterou Mensa nepořádá, avšak myslíte si, že by
se ostatním líbila. Pokud někdo chce slyšet o akcích v sousedním kraji, může si to zvolit,
pokud chce slyšet i o akcích mimo Mensu, může si zvolit odběr komerčních zpráv.
Pokud chcete informovat ostatní o zajímavých aktivitách mimo Mensu, prosím, využívejte neintrusivní metody,
například časopis nebo fórum.</p>


<h3>Obecné zprávy</h3>
<ul>
	<li>Obecné zprávy je
	možné využít pouze a&nbsp;výhradně pro oznámení spojená s
	významnými aktivitami Mensy (odhadem více než 40 účastníků). </li>

	<li>Každý člen může
	zaslat maximálně jednu obecnou zprávu za 15 dnů. </li>

	<li>Příjem zpráv
	z&nbsp;této kategorie má každý člen standardně povolen, může
	si jej však vypnout.</li>

	<li>Do kategorií
	„Komerční zprávy“, „Nabídky práce“ a „Nemensovní nekomerční zprávy“ lze zasílat i
	nemensovní poptávky a nabídky. Odběr těchto zpráv si musí
	každý člen explicitně povolit, má jej standardně zakázán.</li>

	<li>Zprávy směřujte
	pouze do krajů, ve kterých se oznamovaná aktivita <B>koná</B>.
	Členové mají možnost odebírat zprávy z více krajů a sami si
	zvolí, zda mají zájem o akci v daném kraji vědět. Prosím,
	neberte jim tuto svobodu volby a nevnucujte jim informace, o kterých
	si vy myslíte, že je budou zajímat.</li>
  
<li>
  Kategorie „Nemensovní nekomerční zprávy“ je určena pro informování členů o 
  nemensovních projektech či soutěžích <B>zaměřených na inteligenci</B>, nabídky na účast v obdobně orientovaných výzkumech a anketách atd.
</li>
</ul>

<h3>Konference</h3>
<ul>
	<li>Konference
	umožňují dynamickou diskusi a informování o
	akcích místních skupin nebo SIGů – každý účastník
	konference může zasílat zprávy libovolně často. </li>

	<li>Příjem zpráv
	z&nbsp;vybrané konference si musí každý člen nejprve explicitně
	povolit, to mu také umožní do dané konference přispívat.</li>

	<li>Do konferencí lze
	přispívat i přímo odesláním emailu na adresu konference.</li>
</ul>

<h3>Kam zasílat pozvánky?</h3>

<P>Zprávy o akcích SIGů
a místních skupin patří primárně do konferencí. Pouze pozvánky
na výjimečné nebo zcela nové akce, které svým rozsahem
přesahují působnost místní skupiny nebo SIGu, mohou být poslány
do dalších konferencí nebo jako obecné zprávy.</P>
<P>Systém rozesílání
zpráv je navržen tak, aby si každý člen mohl přesně nastavit,
o jaké zprávy má zájem. Díky tomu může intranet vyhovovat
různým lidem s různými preferencemi. Správná funkce systému
však vyžaduje součinnost autorů zpráv – Mensa apriori očekává,
že její členové budou inteligentní, rozumní a zodpovědní, a
nechce restriktivně kontrolovat každou zprávu před jejím
odesláním. Pokud by však některý z účastníků nedbal pravidel
a narušoval funkčnost systému a tím pak důvěru ostatních členů
v účelnost rozesílaných zpráv, je připravena důrazně
zasáhnout. Porušení pravidel může být trestáno až úplným
zamezením přístupu na intranet!</P>



<p>Specifikaci systému naleznete v <a href="/document.php?men=men14.3.0.0&id_c=232" title="nápověda systému zpráv">tomto dokumentu</a>.</p>
<script type="text/javascript">
  var type_sel = document.getElementById('type_sel');
  select_type(type_sel);
</script>
<?php } ?>